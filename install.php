<?php
/**
 * Script de instalación para Zouk Market
 * 
 * Este script inicializa la base de datos y crea las estructuras necesarias
 */

// Incluir archivo de configuración
require_once 'config/config.php';

// Función para ejecutar consultas SQL desde un archivo
function executeSQLFile($conn, $file) {
    $success = true;
    $error = '';
    
    try {
        // Leer el archivo SQL
        $sql = file_get_contents($file);
        
        // Dividir el archivo en consultas individuales
        $queries = explode(';', $sql);
        
        // Ejecutar cada consulta
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                $result = $conn->query($query);
                if (!$result) {
                    $success = false;
                    $error .= "Error en la consulta: " . $conn->error . "<br>";
                }
            }
        }
    } catch (Exception $e) {
        $success = false;
        $error = "Error: " . $e->getMessage();
    }
    
    return [
        'success' => $success,
        'error' => $error
    ];
}

// Verificar si se ha enviado el formulario
$installed = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Ejecutar el archivo SQL
    $result = executeSQLFile($conn, 'db/schema.sql');
    
    if ($result['success']) {
        $installed = true;
    } else {
        $error = $result['error'];
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت ذوق ماركت</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
        }
        .install-container {
            max-width: 800px;
            margin: 50px auto;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            font-size: 2.5rem;
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container install-container">
        <div class="logo">
            <h1>ذوق ماركت</h1>
            <p class="lead">تثبيت النظام</p>
        </div>
        
        <?php if ($installed): ?>
            <div class="alert alert-success">
                <h4><i class="fas fa-check-circle"></i> تم التثبيت بنجاح!</h4>
                <p>تم تثبيت نظام ذوق ماركت بنجاح. يمكنك الآن البدء في استخدام النظام.</p>
                <hr>
                <p>بيانات الدخول الافتراضية للمدير:</p>
                <ul>
                    <li>البريد الإلكتروني: <EMAIL></li>
                    <li>كلمة المرور: password</li>
                </ul>
                <div class="mt-4">
                    <a href="<?php echo APP_URL; ?>/public/index.php" class="btn btn-primary">الذهاب إلى الصفحة الرئيسية</a>
                    <a href="<?php echo APP_URL; ?>/public/login.php" class="btn btn-outline-primary ms-2">تسجيل الدخول</a>
                </div>
            </div>
        <?php elseif (!empty($error)): ?>
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-triangle"></i> حدث خطأ أثناء التثبيت</h4>
                <p><?php echo $error; ?></p>
                <div class="mt-3">
                    <button onclick="window.location.reload()" class="btn btn-primary">المحاولة مرة أخرى</button>
                </div>
            </div>
        <?php else: ?>
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">تثبيت نظام ذوق ماركت</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <p><i class="fas fa-info-circle"></i> سيقوم هذا المعالج بتثبيت نظام ذوق ماركت وإعداد قاعدة البيانات.</p>
                        <p>تأكد من إنشاء قاعدة بيانات باسم <code>zouk_market</code> قبل المتابعة.</p>
                    </div>
                    
                    <h5 class="mt-4">متطلبات النظام:</h5>
                    <ul class="list-group mb-4">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            PHP 7.4 أو أحدث
                            <span class="badge bg-<?php echo version_compare(PHP_VERSION, '7.4.0') >= 0 ? 'success' : 'danger'; ?>">
                                <?php echo PHP_VERSION; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            تفعيل PDO
                            <span class="badge bg-<?php echo extension_loaded('pdo') ? 'success' : 'danger'; ?>">
                                <?php echo extension_loaded('pdo') ? 'مفعل' : 'غير مفعل'; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            تفعيل MySQLi
                            <span class="badge bg-<?php echo extension_loaded('mysqli') ? 'success' : 'danger'; ?>">
                                <?php echo extension_loaded('mysqli') ? 'مفعل' : 'غير مفعل'; ?>
                            </span>
                        </li>
                    </ul>
                    
                    <form method="POST" action="">
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">تثبيت النظام</button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
