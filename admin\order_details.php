<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج الطلب
require_once '../models/Order.php';
require_once '../models/Store.php';

// التحقق من وجود معرف الطلب
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف الطلب غير صالح';
    redirect(APP_URL . '/admin/orders.php');
}

$orderId = intval($_GET['id']);

// إنشاء كائن الطلب
$orderModel = new Order($conn);
$storeModel = new Store($conn);

// الحصول على بيانات الطلب
$order = $orderModel->getById($orderId);

if (!$order) {
    $_SESSION['error'] = 'الطلب غير موجود';
    redirect(APP_URL . '/admin/orders.php');
}

// الحصول على بيانات المتجر
$store = $storeModel->getById($order['store_id']);

// الحصول على بيانات العميل
$sql = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $order['user_id']);
$stmt->execute();
$customer = $stmt->get_result()->fetch_assoc();

// الحصول على عناصر الطلب
$orderItems = $orderModel->getOrderItems($orderId);

// معالجة تحديث حالة الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/admin/order_details.php?id=' . $orderId);
    }
    
    $status = $_POST['status'];
    $validStatuses = ['new', 'processing', 'ready', 'delivering', 'completed', 'cancelled'];
    
    if (in_array($status, $validStatuses)) {
        if ($orderModel->updateStatus($orderId, $status, $order['store_id'])) {
            $_SESSION['success'] = 'تم تحديث حالة الطلب بنجاح';
            
            // إرسال إشعار للعميل
            $statusText = '';
            switch ($status) {
                case 'new': $statusText = 'جديد'; break;
                case 'processing': $statusText = 'قيد التجهيز'; break;
                case 'ready': $statusText = 'جاهز'; break;
                case 'delivering': $statusText = 'قيد التوصيل'; break;
                case 'completed': $statusText = 'مكتمل'; break;
                case 'cancelled': $statusText = 'ملغي'; break;
            }
            
            $sql = "INSERT INTO notifications (user_id, title, message, is_read, created_at)
                    VALUES (?, 'تحديث حالة الطلب #" . $orderId . "', 'تم تحديث حالة طلبك إلى: " . $statusText . "', 0, NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $order['user_id']);
            $stmt->execute();
            
            // تحديث الطلب في الصفحة
            $order = $orderModel->getById($orderId);
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة الطلب';
        }
    } else {
        $_SESSION['error'] = 'حالة الطلب غير صالحة';
    }
}

// معالجة تحديث حالة الدفع
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_payment'])) {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/admin/order_details.php?id=' . $orderId);
    }
    
    $paymentStatus = $_POST['payment_status'];
    $validPaymentStatuses = ['pending', 'paid', 'refunded'];
    
    if (in_array($paymentStatus, $validPaymentStatuses)) {
        if ($orderModel->updatePaymentStatus($orderId, $paymentStatus)) {
            $_SESSION['success'] = 'تم تحديث حالة الدفع بنجاح';
            
            // إرسال إشعار للعميل
            $paymentStatusText = '';
            switch ($paymentStatus) {
                case 'pending': $paymentStatusText = 'معلق'; break;
                case 'paid': $paymentStatusText = 'مدفوع'; break;
                case 'refunded': $paymentStatusText = 'مسترجع'; break;
            }
            
            $sql = "INSERT INTO notifications (user_id, title, message, is_read, created_at)
                    VALUES (?, 'تحديث حالة الدفع للطلب #" . $orderId . "', 'تم تحديث حالة الدفع لطلبك إلى: " . $paymentStatusText . "', 0, NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $order['user_id']);
            $stmt->execute();
            
            // تحديث الطلب في الصفحة
            $order = $orderModel->getById($orderId);
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة الدفع';
        }
    } else {
        $_SESSION['error'] = 'حالة الدفع غير صالحة';
    }
}

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تفاصيل الطلب #<?php echo $order['id']; ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="<?php echo APP_URL; ?>/admin/orders.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> العودة إلى قائمة الطلبات
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة الطلب
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- معلومات الطلب -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">معلومات الطلب</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الطلب</label>
                                        <p class="form-control">#<?php echo $order['id']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">تاريخ الطلب</label>
                                        <p class="form-control"><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">المتجر</label>
                                        <p class="form-control">
                                            <a href="<?php echo APP_URL; ?>/admin/store_details.php?id=<?php echo $store['id']; ?>">
                                                <?php echo $store['name']; ?>
                                            </a>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">إجمالي المبلغ</label>
                                        <p class="form-control"><?php echo number_format($order['total_amount'], 2); ?> شيكل</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">طريقة الدفع</label>
                                        <p class="form-control">
                                            <?php
                                            switch ($order['payment_method']) {
                                                case 'cash':
                                                    echo 'نقدي';
                                                    break;
                                                case 'card':
                                                    echo 'بطاقة ائتمان';
                                                    break;
                                                case 'wallet':
                                                    echo 'محفظة إلكترونية';
                                                    break;
                                                default:
                                                    echo $order['payment_method'];
                                            }
                                            ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">حالة الدفع</label>
                                        <p class="form-control">
                                            <?php
                                            $paymentStatusClass = '';
                                            $paymentStatusText = '';
                                            switch ($order['payment_status']) {
                                                case 'pending':
                                                    $paymentStatusClass = 'bg-warning text-dark';
                                                    $paymentStatusText = 'معلق';
                                                    break;
                                                case 'paid':
                                                    $paymentStatusClass = 'bg-success';
                                                    $paymentStatusText = 'مدفوع';
                                                    break;
                                                case 'refunded':
                                                    $paymentStatusClass = 'bg-danger';
                                                    $paymentStatusText = 'مسترجع';
                                                    break;
                                            }
                                            echo '<span class="badge ' . $paymentStatusClass . '">' . $paymentStatusText . '</span>';
                                            ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">حالة الطلب</label>
                                        <p class="form-control">
                                            <?php
                                            $statusClass = '';
                                            $statusText = '';
                                            switch ($order['status']) {
                                                case 'new':
                                                    $statusClass = 'bg-primary';
                                                    $statusText = 'جديد';
                                                    break;
                                                case 'processing':
                                                    $statusClass = 'bg-warning text-dark';
                                                    $statusText = 'قيد التجهيز';
                                                    break;
                                                case 'ready':
                                                    $statusClass = 'bg-info';
                                                    $statusText = 'جاهز';
                                                    break;
                                                case 'delivering':
                                                    $statusClass = 'bg-info';
                                                    $statusText = 'قيد التوصيل';
                                                    break;
                                                case 'completed':
                                                    $statusClass = 'bg-success';
                                                    $statusText = 'مكتمل';
                                                    break;
                                                case 'cancelled':
                                                    $statusClass = 'bg-danger';
                                                    $statusText = 'ملغي';
                                                    break;
                                            }
                                            echo '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';
                                            ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">آخر تحديث</label>
                                        <p class="form-control"><?php echo date('Y-m-d H:i', strtotime($order['updated_at'])); ?></p>
                                    </div>
                                </div>
                                <?php if (!empty($order['notes'])): ?>
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">ملاحظات</label>
                                            <p class="form-control"><?php echo $order['notes']; ?></p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">معلومات العميل</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">اسم العميل</label>
                                        <p class="form-control"><?php echo $customer['name']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <p class="form-control"><?php echo $customer['email']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <p class="form-control"><?php echo $customer['phone']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">رقم هاتف التوصيل</label>
                                        <p class="form-control"><?php echo $order['phone']; ?></p>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">عنوان التوصيل</label>
                                        <p class="form-control"><?php echo $order['address']; ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">تحديث الطلب</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <form action="" method="POST">
                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                        <input type="hidden" name="update_status" value="1">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">تغيير حالة الطلب</label>
                                            <select name="status" id="status" class="form-select">
                                                <option value="new" <?php echo $order['status'] === 'new' ? 'selected' : ''; ?>>جديد</option>
                                                <option value="processing" <?php echo $order['status'] === 'processing' ? 'selected' : ''; ?>>قيد التجهيز</option>
                                                <option value="ready" <?php echo $order['status'] === 'ready' ? 'selected' : ''; ?>>جاهز</option>
                                                <option value="delivering" <?php echo $order['status'] === 'delivering' ? 'selected' : ''; ?>>قيد التوصيل</option>
                                                <option value="completed" <?php echo $order['status'] === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                                                <option value="cancelled" <?php echo $order['status'] === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                                            </select>
                                        </div>
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-primary">تحديث الحالة</button>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <form action="" method="POST">
                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                        <input type="hidden" name="update_payment" value="1">
                                        <div class="mb-3">
                                            <label for="payment_status" class="form-label">تغيير حالة الدفع</label>
                                            <select name="payment_status" id="payment_status" class="form-select">
                                                <option value="pending" <?php echo $order['payment_status'] === 'pending' ? 'selected' : ''; ?>>معلق</option>
                                                <option value="paid" <?php echo $order['payment_status'] === 'paid' ? 'selected' : ''; ?>>مدفوع</option>
                                                <option value="refunded" <?php echo $order['payment_status'] === 'refunded' ? 'selected' : ''; ?>>مسترجع</option>
                                            </select>
                                        </div>
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-primary">تحديث حالة الدفع</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- عناصر الطلب -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">عناصر الطلب</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الصورة</th>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orderItems as $item): ?>
                                    <tr>
                                        <td>
                                            <img src="<?php echo APP_URL; ?>/public/uploads/products/<?php echo $item['product_image']; ?>" alt="<?php echo $item['product_name']; ?>" class="img-thumbnail" width="50">
                                        </td>
                                        <td>
                                            <a href="<?php echo APP_URL; ?>/admin/product_details.php?id=<?php echo $item['product_id']; ?>">
                                                <?php echo $item['product_name']; ?>
                                            </a>
                                        </td>
                                        <td><?php echo number_format($item['price'], 2); ?> شيكل</td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td><?php echo number_format($item['price'] * $item['quantity'], 2); ?> شيكل</td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>المجموع:</strong></td>
                                    <td><strong><?php echo number_format($order['total_amount'], 2); ?> شيكل</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- سجل تحديثات الطلب -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">سجل تحديثات الطلب</h5>
                </div>
                <div class="card-body">
                    <?php
                    $sql = "SELECT * FROM order_history WHERE order_id = ? ORDER BY created_at DESC";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("i", $orderId);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $history = [];
                    
                    if ($result && $result->num_rows > 0) {
                        while ($row = $result->fetch_assoc()) {
                            $history[] = $row;
                        }
                    }
                    ?>
                    
                    <?php if (empty($history)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا يوجد سجل تحديثات لهذا الطلب.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الحالة السابقة</th>
                                        <th>الحالة الجديدة</th>
                                        <th>بواسطة</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($history as $entry): ?>
                                        <tr>
                                            <td><?php echo date('Y-m-d H:i', strtotime($entry['created_at'])); ?></td>
                                            <td>
                                                <?php
                                                $oldStatusClass = '';
                                                $oldStatusText = '';
                                                switch ($entry['old_status']) {
                                                    case 'new':
                                                        $oldStatusClass = 'bg-primary';
                                                        $oldStatusText = 'جديد';
                                                        break;
                                                    case 'processing':
                                                        $oldStatusClass = 'bg-warning text-dark';
                                                        $oldStatusText = 'قيد التجهيز';
                                                        break;
                                                    case 'ready':
                                                        $oldStatusClass = 'bg-info';
                                                        $oldStatusText = 'جاهز';
                                                        break;
                                                    case 'delivering':
                                                        $oldStatusClass = 'bg-info';
                                                        $oldStatusText = 'قيد التوصيل';
                                                        break;
                                                    case 'completed':
                                                        $oldStatusClass = 'bg-success';
                                                        $oldStatusText = 'مكتمل';
                                                        break;
                                                    case 'cancelled':
                                                        $oldStatusClass = 'bg-danger';
                                                        $oldStatusText = 'ملغي';
                                                        break;
                                                }
                                                echo '<span class="badge ' . $oldStatusClass . '">' . $oldStatusText . '</span>';
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $newStatusClass = '';
                                                $newStatusText = '';
                                                switch ($entry['new_status']) {
                                                    case 'new':
                                                        $newStatusClass = 'bg-primary';
                                                        $newStatusText = 'جديد';
                                                        break;
                                                    case 'processing':
                                                        $newStatusClass = 'bg-warning text-dark';
                                                        $newStatusText = 'قيد التجهيز';
                                                        break;
                                                    case 'ready':
                                                        $newStatusClass = 'bg-info';
                                                        $newStatusText = 'جاهز';
                                                        break;
                                                    case 'delivering':
                                                        $newStatusClass = 'bg-info';
                                                        $newStatusText = 'قيد التوصيل';
                                                        break;
                                                    case 'completed':
                                                        $newStatusClass = 'bg-success';
                                                        $newStatusText = 'مكتمل';
                                                        break;
                                                    case 'cancelled':
                                                        $newStatusClass = 'bg-danger';
                                                        $newStatusText = 'ملغي';
                                                        break;
                                                }
                                                echo '<span class="badge ' . $newStatusClass . '">' . $newStatusText . '</span>';
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $sql = "SELECT name FROM users WHERE id = ?";
                                                $stmt = $conn->prepare($sql);
                                                $stmt->bind_param("i", $entry['user_id']);
                                                $stmt->execute();
                                                $user = $stmt->get_result()->fetch_assoc();
                                                echo $user['name'] ?? 'غير معروف';
                                                ?>
                                            </td>
                                            <td><?php echo $entry['notes'] ?? '-'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
