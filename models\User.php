<?php
/**
 * User Model
 *
 * Handles all user-related operations
 */
class User {
    private $conn;
    private $table = 'users';

    // User properties
    public $id;
    public $name;
    public $email;
    public $password;
    public $phone;
    public $role;
    public $is_active;
    public $created_at;
    public $updated_at;

    /**
     * Constructor
     *
     * @param mysqli $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Register a new user
     *
     * @return boolean
     */
    public function register() {
        // Hash password
        $hashed_password = password_hash($this->password, PASSWORD_DEFAULT);

        // Create query
        $query = "INSERT INTO " . $this->table . "
                  SET name = ?,
                      email = ?,
                      password = ?,
                      phone = ?,
                      role = ?,
                      is_active = ?,
                      created_at = NOW(),
                      updated_at = NOW()";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->phone = htmlspecialchars(strip_tags($this->phone));
        $this->role = htmlspecialchars(strip_tags($this->role));
        $this->is_active = $this->is_active ?? 1;

        // Bind data
        $stmt->bind_param("sssssi",
            $this->name,
            $this->email,
            $hashed_password,
            $this->phone,
            $this->role,
            $this->is_active
        );

        // Execute query
        if($stmt->execute()) {
            $this->id = $this->conn->insert_id;
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Login user
     *
     * @param string $email
     * @param string $password
     * @return array|boolean
     */
    public function login($email, $password) {
        // Create query
        $query = "SELECT id, name, email, password, role, is_active
                  FROM " . $this->table . "
                  WHERE email = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind email
        $stmt->bind_param("s", $email);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        // Check if user exists
        if($result->num_rows > 0) {
            $user = $result->fetch_assoc();

            // Verify password
            if(password_verify($password, $user['password'])) {
                // Check if user is active
                if($user['is_active'] == 0) {
                    return ['error' => 'الحساب غير مفعل. يرجى الاتصال بالإدارة.'];
                }

                // Remove password from array
                unset($user['password']);

                return $user;
            } else {
                return ['error' => 'كلمة المرور غير صحيحة'];
            }
        } else {
            return ['error' => 'البريد الإلكتروني غير مسجل'];
        }
    }

    /**
     * Get user by ID
     *
     * @param int $id
     * @return array|boolean
     */
    public function getById($id) {
        // Create query
        $query = "SELECT id, name, email, password, phone, address, profile_image, role, is_active, created_at, updated_at
                  FROM " . $this->table . "
                  WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind ID
        $stmt->bind_param("i", $id);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        if($result->num_rows > 0) {
            return $result->fetch_assoc();
        } else {
            return false;
        }
    }

    /**
     * Get all users
     *
     * @param string $role Optional role filter
     * @return array
     */
    public function getAll($role = null) {
        // Create query
        $query = "SELECT id, name, email, phone, role, is_active, created_at, updated_at
                  FROM " . $this->table;

        // Add role filter if provided
        if($role) {
            $query .= " WHERE role = ?";
        }

        $query .= " ORDER BY created_at DESC";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind role if provided
        if($role) {
            $stmt->bind_param("s", $role);
        }

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        $users = [];

        while($row = $result->fetch_assoc()) {
            $users[] = $row;
        }

        return $users;
    }

    /**
     * Update user
     *
     * @return boolean
     */
    public function update() {
        // Create query
        $query = "UPDATE " . $this->table . "
                  SET name = ?,
                      phone = ?,
                      is_active = ?,
                      updated_at = NOW()
                  WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->phone = htmlspecialchars(strip_tags($this->phone));
        $this->is_active = htmlspecialchars(strip_tags($this->is_active));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind data
        $stmt->bind_param("ssii",
            $this->name,
            $this->phone,
            $this->is_active,
            $this->id
        );

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Update user password
     *
     * @return boolean
     */
    public function updatePassword() {
        // Hash password
        $hashed_password = password_hash($this->password, PASSWORD_DEFAULT);

        // Create query
        $query = "UPDATE " . $this->table . "
                  SET password = ?,
                      updated_at = NOW()
                  WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bind_param("si", $hashed_password, $this->id);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Delete user
     *
     * @param int $id
     * @return boolean
     */
    public function delete($id) {
        // Create query
        $query = "DELETE FROM " . $this->table . " WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bind_param("i", $id);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Check if email exists
     *
     * @param string $email
     * @return boolean
     */
    public function emailExists($email) {
        // Create query
        $query = "SELECT id FROM " . $this->table . " WHERE email = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind email
        $stmt->bind_param("s", $email);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        return $result->num_rows > 0;
    }

    /**
     * Update user profile
     *
     * @return boolean
     */
    public function updateProfile() {
        // Create query
        $query = "UPDATE " . $this->table . "
                  SET name = ?,
                      email = ?,
                      phone = ?,
                      address = ?,
                      profile_image = ?,
                      updated_at = NOW()
                  WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->phone = htmlspecialchars(strip_tags($this->phone));
        $this->address = htmlspecialchars(strip_tags($this->address));
        $this->profile_image = htmlspecialchars(strip_tags($this->profile_image));

        // Bind data
        $stmt->bind_param("sssssi",
            $this->name,
            $this->email,
            $this->phone,
            $this->address,
            $this->profile_image,
            $this->id
        );

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }
}
