<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المنتج
require_once '../models/Product.php';
require_once '../models/Store.php';

// إنشاء كائن المنتج
$productModel = new Product($conn);
$storeModel = new Store($conn);

// معالجة الإجراءات
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $productId = isset($_GET['id']) ? intval($_GET['id']) : 0;

    // التحقق من وجود المنتج
    $product = $productModel->getById($productId);

    if (!$product) {
        $_SESSION['error'] = 'المنتج غير موجود';
        redirect(APP_URL . '/admin/products.php');
    }

    // تنفيذ الإجراء المطلوب
    switch ($action) {
        case 'activate':
            // تفعيل المنتج
            $productModel = new Product($conn);
            $productModel->id = $productId;
            $productModel->is_active = 1;
            if ($productModel->update()) {
                $_SESSION['success'] = 'تم تفعيل المنتج بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء تفعيل المنتج';
            }
            break;

        case 'deactivate':
            // تعطيل المنتج
            $productModel = new Product($conn);
            $productModel->id = $productId;
            $productModel->is_active = 0;
            if ($productModel->update()) {
                $_SESSION['success'] = 'تم تعطيل المنتج بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء تعطيل المنتج';
            }
            break;

        case 'feature':
            // تمييز المنتج
            $productModel = new Product($conn);
            $productModel->id = $productId;
            $productModel->is_featured = 1;
            if ($productModel->update()) {
                $_SESSION['success'] = 'تم تمييز المنتج بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء تمييز المنتج';
            }
            break;

        case 'unfeature':
            // إلغاء تمييز المنتج
            $productModel = new Product($conn);
            $productModel->id = $productId;
            $productModel->is_featured = 0;
            if ($productModel->update()) {
                $_SESSION['success'] = 'تم إلغاء تمييز المنتج بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء إلغاء تمييز المنتج';
            }
            break;

        case 'delete':
            // حذف المنتج
            if ($productModel->delete($productId, $product['store_id'])) {
                $_SESSION['success'] = 'تم حذف المنتج بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف المنتج';
            }
            break;
    }

    redirect(APP_URL . '/admin/products.php');
}

// الحصول على معايير التصفية
$storeId = isset($_GET['store_id']) ? intval($_GET['store_id']) : null;
$categoryId = isset($_GET['category_id']) ? intval($_GET['category_id']) : null;
$activeOnly = isset($_GET['active']) ? ($_GET['active'] == '1') : null;
$featuredOnly = isset($_GET['featured']) ? ($_GET['featured'] == '1') : null;
$searchTerm = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// الحصول على قائمة المنتجات
$sql = "SELECT p.*,
        COALESCE(pc.name, 'غير مصنف') as category_name,
        COALESCE(s.name, 'غير معروف') as store_name
        FROM products p
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        LEFT JOIN stores s ON p.store_id = s.id
        WHERE 1=1";

// إضافة معايير التصفية
$params = [];
$types = "";

if ($storeId) {
    $sql .= " AND p.store_id = ?";
    $params[] = $storeId;
    $types .= "i";
}

if ($categoryId) {
    $sql .= " AND p.category_id = ?";
    $params[] = $categoryId;
    $types .= "i";
}

// التحقق من حالة المنتجات (نشطة أو غير نشطة)
if (isset($_GET['active'])) {
    $isActive = $_GET['active'] == '1' ? 1 : 0;
    $sql .= " AND p.is_active = ?";
    $params[] = $isActive;
    $types .= "i";
}

// التحقق من حالة التمييز (مميزة أو غير مميزة)
if (isset($_GET['featured'])) {
    $isFeatured = $_GET['featured'] == '1' ? 1 : 0;
    $sql .= " AND p.is_featured = ?";
    $params[] = $isFeatured;
    $types .= "i";
}

if (!empty($searchTerm)) {
    $sql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
    $searchParam = "%" . $searchTerm . "%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $types .= "ss";
}

$sql .= " ORDER BY p.created_at DESC";

// تنفيذ الاستعلام
$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
$products = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $products[] = $row;
    }
}

// الحصول على قائمة المتاجر للفلتر
$stores = $storeModel->getAll(null, true);

// الحصول على قائمة فئات المنتجات للفلتر
$sql = "SELECT pc.*, s.name as store_name
        FROM product_categories pc
        JOIN stores s ON pc.store_id = s.id
        ORDER BY s.name, pc.name";
$result = $conn->query($sql);
$categories = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
}

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المنتجات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="<?php echo APP_URL; ?>/admin/add_product.php" class="btn btn-sm btn-success me-2">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </a>
                    <a href="<?php echo APP_URL; ?>/admin/stores.php" class="btn btn-sm btn-outline-secondary me-2">
                        <i class="fas fa-store"></i> إدارة المتاجر
                    </a>
                </div>
            </div>

            <!-- فلتر المنتجات -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">فلتر المنتجات</h5>
                </div>
                <div class="card-body">
                    <form action="" method="GET">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="store_id" class="form-label">المتجر</label>
                                <select name="store_id" id="store_id" class="form-select">
                                    <option value="">جميع المتاجر</option>
                                    <?php foreach ($stores as $store): ?>
                                        <option value="<?php echo $store['id']; ?>" <?php echo $storeId == $store['id'] ? 'selected' : ''; ?>>
                                            <?php echo $store['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="category_id" class="form-label">الفئة</label>
                                <select name="category_id" id="category_id" class="form-select">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" <?php echo $categoryId == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo $category['name']; ?> (<?php echo $category['store_name']; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="active" class="form-label">الحالة</label>
                                <select name="active" id="active" class="form-select">
                                    <option value="">الكل</option>
                                    <option value="1" <?php echo isset($_GET['active']) && $_GET['active'] == '1' ? 'selected' : ''; ?>>المنتجات النشطة فقط</option>
                                    <option value="0" <?php echo isset($_GET['active']) && $_GET['active'] == '0' ? 'selected' : ''; ?>>المنتجات غير النشطة فقط</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="featured" class="form-label">التمييز</label>
                                <select name="featured" id="featured" class="form-select">
                                    <option value="">الكل</option>
                                    <option value="1" <?php echo isset($_GET['featured']) && $_GET['featured'] == '1' ? 'selected' : ''; ?>>المنتجات المميزة فقط</option>
                                    <option value="0" <?php echo isset($_GET['featured']) && $_GET['featured'] == '0' ? 'selected' : ''; ?>>المنتجات غير المميزة فقط</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="search" class="form-label">بحث</label>
                                <input type="text" name="search" id="search" class="form-control" value="<?php echo $searchTerm; ?>" placeholder="اسم المنتج...">
                            </div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">تصفية</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة المنتجات -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">قائمة المنتجات</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($products)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد منتجات تطابق معايير البحث.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>المتجر</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>الحالة</th>
                                        <th>مميز</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td><?php echo $product['id']; ?></td>
                                            <td>
                                                <img src="<?php echo APP_URL; ?>/public/uploads/products/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" class="img-thumbnail" width="50">
                                            </td>
                                            <td><?php echo $product['name']; ?></td>
                                            <td><?php echo $product['store_name']; ?></td>
                                            <td><?php echo $product['category_name']; ?></td>
                                            <td><?php echo number_format($product['price'], 2); ?> شيكل</td>
                                            <td><?php echo $product['stock_quantity']; ?></td>
                                            <td>
                                                <?php if ($product['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($product['is_featured']): ?>
                                                    <span class="badge bg-warning text-dark">مميز</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير مميز</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo APP_URL; ?>/admin/product_details.php?id=<?php echo $product['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($product['is_active']): ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/products.php?action=deactivate&id=<?php echo $product['id']; ?>" class="btn btn-sm btn-warning" title="تعطيل" onclick="return confirm('هل أنت متأكد من تعطيل هذا المنتج؟')">
                                                            <i class="fas fa-ban"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/products.php?action=activate&id=<?php echo $product['id']; ?>" class="btn btn-sm btn-success" title="تفعيل" onclick="return confirm('هل أنت متأكد من تفعيل هذا المنتج؟')">
                                                            <i class="fas fa-check"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($product['is_featured']): ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/products.php?action=unfeature&id=<?php echo $product['id']; ?>" class="btn btn-sm btn-secondary" title="إلغاء التمييز" onclick="return confirm('هل أنت متأكد من إلغاء تمييز هذا المنتج؟')">
                                                            <i class="fas fa-star-half-alt"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/products.php?action=feature&id=<?php echo $product['id']; ?>" class="btn btn-sm btn-warning text-dark" title="تمييز" onclick="return confirm('هل أنت متأكد من تمييز هذا المنتج؟')">
                                                            <i class="fas fa-star"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <a href="<?php echo APP_URL; ?>/admin/products.php?action=delete&id=<?php echo $product['id']; ?>" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- إحصائيات المنتجات -->
            <div class="row mt-4">
                <div class="col-md-4 mb-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">إجمالي المنتجات</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM products";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-shopping-basket fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">المنتجات النشطة</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM products WHERE is_active = 1";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">المنتجات المميزة</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM products WHERE is_featured = 1";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-star fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
