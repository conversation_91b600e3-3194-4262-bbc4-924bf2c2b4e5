<div class="position-sticky pt-3">
    <div class="sidebar-heading">لوحة التحكم</div>
    <ul class="nav flex-column sidebar-menu">
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>" href="<?php echo APP_URL; ?>/admin/dashboard.php">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>" href="<?php echo APP_URL; ?>/admin/users.php">
                <i class="fas fa-users"></i> المستخدمين
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'stores.php' || basename($_SERVER['PHP_SELF']) == 'pending_stores.php' ? 'active' : ''; ?>" href="<?php echo APP_URL; ?>/admin/stores.php">
                <i class="fas fa-store"></i> المتاجر
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'categories.php' ? 'active' : ''; ?>" href="<?php echo APP_URL; ?>/admin/categories.php">
                <i class="fas fa-tags"></i> الفئات
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'products.php' ? 'active' : ''; ?>" href="<?php echo APP_URL; ?>/admin/products.php">
                <i class="fas fa-shopping-basket"></i> المنتجات
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'orders.php' ? 'active' : ''; ?>" href="<?php echo APP_URL; ?>/admin/orders.php">
                <i class="fas fa-shopping-cart"></i> الطلبات
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'reviews.php' ? 'active' : ''; ?>" href="<?php echo APP_URL; ?>/admin/reviews.php">
                <i class="fas fa-star"></i> التقييمات
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'reports.php' ? 'active' : ''; ?>" href="<?php echo APP_URL; ?>/admin/reports.php">
                <i class="fas fa-chart-bar"></i> التقارير
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>" href="<?php echo APP_URL; ?>/admin/settings.php">
                <i class="fas fa-cog"></i> الإعدادات
            </a>
        </li>
    </ul>

    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
        <span>إجراءات سريعة</span>
    </h6>
    <ul class="nav flex-column mb-2">
        <li class="nav-item">
            <a class="nav-link" href="<?php echo APP_URL; ?>/admin/pending_stores.php">
                <i class="fas fa-clipboard-list"></i> طلبات المتاجر الجديدة
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="<?php echo APP_URL; ?>/public/index.php" target="_blank">
                <i class="fas fa-external-link-alt"></i> عرض الموقع
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="<?php echo APP_URL; ?>/public/logout.php">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </li>
    </ul>
</div>
