<?php
// تضمين ملف التكوين
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// تضمين النماذج
require_once '../models/Product.php';
require_once '../models/Store.php';
require_once '../models/ProductCategory.php';

// إنشاء كائنات النماذج
$productModel = new Product($conn);
$storeModel = new Store($conn);
$categoryModel = new ProductCategory($conn);

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/admin/add_product.php');
    }

    // الحصول على البيانات من النموذج
    $name = sanitize($_POST['name']);
    $description = sanitize($_POST['description']);
    $price = floatval($_POST['price']);
    $stock_quantity = intval($_POST['stock_quantity']);
    $store_id = intval($_POST['store_id']);
    $category_id = intval($_POST['category_id']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;

    // التحقق من البيانات
    $errors = [];

    if (empty($name)) {
        $errors[] = 'يرجى إدخال اسم المنتج';
    }

    if (empty($description)) {
        $errors[] = 'يرجى إدخال وصف المنتج';
    }

    if ($price <= 0) {
        $errors[] = 'يجب أن يكون السعر أكبر من صفر';
    }

    if ($stock_quantity < 0) {
        $errors[] = 'يجب أن تكون الكمية صفر أو أكبر';
    }

    if ($store_id <= 0) {
        $errors[] = 'يرجى اختيار متجر';
    }

    if ($category_id <= 0) {
        $errors[] = 'يرجى اختيار فئة';
    }

    // التحقق من تحميل الصورة
    $image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $max_size = 2 * 1024 * 1024; // 2MB

        if (!in_array($_FILES['image']['type'], $allowed_types)) {
            $errors[] = 'نوع الملف غير مدعوم. الأنواع المدعومة هي: JPG, PNG, GIF, WEBP';
        }

        if ($_FILES['image']['size'] > $max_size) {
            $errors[] = 'حجم الملف كبير جدًا. الحد الأقصى هو 2 ميجابايت';
        }

        if (empty($errors)) {
            // إنشاء اسم فريد للملف
            $image = uniqid() . '_' . basename($_FILES['image']['name']);
            $upload_dir = PUBLIC_PATH . '/uploads/products/';

            // التأكد من وجود المجلد
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            // نقل الملف
            if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_dir . $image)) {
                $errors[] = 'حدث خطأ أثناء تحميل الصورة';
            }
        }
    } else {
        $errors[] = 'يرجى تحميل صورة للمنتج';
    }

    // إذا لم تكن هناك أخطاء، قم بإضافة المنتج
    if (empty($errors)) {
        $productModel->name = $name;
        $productModel->description = $description;
        $productModel->price = $price;
        $productModel->stock_quantity = $stock_quantity;
        $productModel->store_id = $store_id;
        $productModel->category_id = $category_id;
        $productModel->image = $image;
        $productModel->is_active = $is_active;
        $productModel->is_featured = $is_featured;

        if ($productModel->create()) {
            $_SESSION['success'] = 'تمت إضافة المنتج بنجاح';
            redirect(APP_URL . '/admin/products.php');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء إضافة المنتج';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// الحصول على قائمة المتاجر
$stores = $storeModel->getAll(null, true);

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إضافة منتج جديد</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="<?php echo APP_URL; ?>/admin/products.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> العودة إلى قائمة المنتجات
                    </a>
                </div>
            </div>

            <!-- نموذج إضافة منتج -->
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معلومات المنتج</h5>
                </div>
                <div class="card-body">
                    <form action="" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required value="<?php echo isset($_POST['name']) ? $_POST['name'] : ''; ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="price" class="form-label">السعر (شيكل) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required value="<?php echo isset($_POST['price']) ? $_POST['price'] : ''; ?>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="store_id" class="form-label">المتجر <span class="text-danger">*</span></label>
                                <select class="form-select" id="store_id" name="store_id" required>
                                    <option value="">اختر المتجر</option>
                                    <?php foreach ($stores as $store): ?>
                                        <option value="<?php echo $store['id']; ?>" <?php echo isset($_POST['store_id']) && $_POST['store_id'] == $store['id'] ? 'selected' : ''; ?>>
                                            <?php echo $store['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="category_id" class="form-label">الفئة <span class="text-danger">*</span></label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value="">اختر الفئة</option>
                                    <!-- سيتم تحميل الفئات بناءً على المتجر المختار باستخدام JavaScript -->
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="stock_quantity" class="form-label">الكمية المتوفرة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" min="0" required value="<?php echo isset($_POST['stock_quantity']) ? $_POST['stock_quantity'] : ''; ?>">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="image" class="form-label">صورة المنتج <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" id="image" name="image" accept="image/*" required>
                                <small class="text-muted">الحد الأقصى للحجم: 2 ميجابايت. الأنواع المدعومة: JPG, PNG, GIF, WEBP</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المنتج <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="4" required><?php echo isset($_POST['description']) ? $_POST['description'] : ''; ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo !isset($_POST['is_active']) || isset($_POST['is_active']) && $_POST['is_active'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">
                                        منتج نشط (متاح للبيع)
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" <?php echo isset($_POST['is_featured']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_featured">
                                        منتج مميز (يظهر في الصفحة الرئيسية)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">إضافة المنتج</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحميل الفئات بناءً على المتجر المختار
document.getElementById('store_id').addEventListener('change', function() {
    const storeId = this.value;
    const categorySelect = document.getElementById('category_id');
    
    // مسح الخيارات الحالية
    categorySelect.innerHTML = '<option value="">اختر الفئة</option>';
    
    if (storeId) {
        // إرسال طلب AJAX للحصول على فئات المتجر
        fetch(`<?php echo APP_URL; ?>/admin/ajax/get_categories.php?store_id=${storeId}`)
            .then(response => response.json())
            .then(data => {
                if (data.length > 0) {
                    data.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        categorySelect.appendChild(option);
                    });
                } else {
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = 'لا توجد فئات لهذا المتجر';
                    categorySelect.appendChild(option);
                }
            })
            .catch(error => {
                console.error('Error fetching categories:', error);
            });
    }
});
</script>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
