<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المتجر
require_once '../models/Store.php';
require_once '../models/Order.php';

// إنشاء كائن المتجر والطلب
$storeModel = new Store($conn);
$orderModel = new Order($conn);

// الحصول على معايير التصفية
$reportType = isset($_GET['report_type']) ? $_GET['report_type'] : 'sales';
$storeId = isset($_GET['store_id']) ? intval($_GET['store_id']) : null;
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-d', strtotime('-30 days'));
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d');
$groupBy = isset($_GET['group_by']) ? $_GET['group_by'] : 'day';

// الحصول على قائمة المتاجر للفلتر
$stores = $storeModel->getAll(null, true);

// تحضير بيانات التقرير
$reportData = [];
$chartLabels = [];
$chartData = [];

switch ($reportType) {
    case 'sales':
        // تقرير المبيعات
        $sql = "SELECT ";
        
        // تحديد طريقة التجميع
        switch ($groupBy) {
            case 'day':
                $sql .= "DATE(o.created_at) as date, ";
                break;
            case 'month':
                $sql .= "DATE_FORMAT(o.created_at, '%Y-%m') as date, ";
                break;
            case 'year':
                $sql .= "YEAR(o.created_at) as date, ";
                break;
        }
        
        $sql .= "COUNT(o.id) as order_count, SUM(o.total_amount) as total_sales 
                FROM orders o 
                WHERE o.status = 'completed' AND o.payment_status = 'paid'";
        
        // إضافة معايير التصفية
        $params = [];
        $types = "";
        
        if ($storeId) {
            $sql .= " AND o.store_id = ?";
            $params[] = $storeId;
            $types .= "i";
        }
        
        if ($dateFrom) {
            $sql .= " AND DATE(o.created_at) >= ?";
            $params[] = $dateFrom;
            $types .= "s";
        }
        
        if ($dateTo) {
            $sql .= " AND DATE(o.created_at) <= ?";
            $params[] = $dateTo;
            $types .= "s";
        }
        
        // تجميع النتائج
        $sql .= " GROUP BY date ORDER BY date";
        
        // تنفيذ الاستعلام
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $reportData[] = $row;
                $chartLabels[] = $row['date'];
                $chartData[] = $row['total_sales'];
            }
        }
        break;
        
    case 'products':
        // تقرير المنتجات الأكثر مبيعاً
        $sql = "SELECT p.id, p.name, p.image, s.name as store_name, 
                COUNT(oi.id) as order_count, SUM(oi.quantity) as total_quantity, 
                SUM(oi.quantity * oi.price) as total_sales 
                FROM order_items oi 
                JOIN products p ON oi.product_id = p.id 
                JOIN orders o ON oi.order_id = o.id 
                JOIN stores s ON p.store_id = s.id 
                WHERE o.status = 'completed' AND o.payment_status = 'paid'";
        
        // إضافة معايير التصفية
        $params = [];
        $types = "";
        
        if ($storeId) {
            $sql .= " AND p.store_id = ?";
            $params[] = $storeId;
            $types .= "i";
        }
        
        if ($dateFrom) {
            $sql .= " AND DATE(o.created_at) >= ?";
            $params[] = $dateFrom;
            $types .= "s";
        }
        
        if ($dateTo) {
            $sql .= " AND DATE(o.created_at) <= ?";
            $params[] = $dateTo;
            $types .= "s";
        }
        
        // تجميع النتائج
        $sql .= " GROUP BY p.id ORDER BY total_quantity DESC LIMIT 10";
        
        // تنفيذ الاستعلام
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $reportData[] = $row;
                $chartLabels[] = $row['name'];
                $chartData[] = $row['total_quantity'];
            }
        }
        break;
        
    case 'stores':
        // تقرير المتاجر الأكثر مبيعاً
        $sql = "SELECT s.id, s.name, s.logo, 
                COUNT(o.id) as order_count, SUM(o.total_amount) as total_sales 
                FROM orders o 
                JOIN stores s ON o.store_id = s.id 
                WHERE o.status = 'completed' AND o.payment_status = 'paid'";
        
        // إضافة معايير التصفية
        $params = [];
        $types = "";
        
        if ($storeId) {
            $sql .= " AND o.store_id = ?";
            $params[] = $storeId;
            $types .= "i";
        }
        
        if ($dateFrom) {
            $sql .= " AND DATE(o.created_at) >= ?";
            $params[] = $dateFrom;
            $types .= "s";
        }
        
        if ($dateTo) {
            $sql .= " AND DATE(o.created_at) <= ?";
            $params[] = $dateTo;
            $types .= "s";
        }
        
        // تجميع النتائج
        $sql .= " GROUP BY s.id ORDER BY total_sales DESC LIMIT 10";
        
        // تنفيذ الاستعلام
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $reportData[] = $row;
                $chartLabels[] = $row['name'];
                $chartData[] = $row['total_sales'];
            }
        }
        break;
        
    case 'customers':
        // تقرير العملاء الأكثر شراءً
        $sql = "SELECT u.id, u.name, u.email, 
                COUNT(o.id) as order_count, SUM(o.total_amount) as total_spent 
                FROM orders o 
                JOIN users u ON o.user_id = u.id 
                WHERE o.status = 'completed' AND o.payment_status = 'paid'";
        
        // إضافة معايير التصفية
        $params = [];
        $types = "";
        
        if ($storeId) {
            $sql .= " AND o.store_id = ?";
            $params[] = $storeId;
            $types .= "i";
        }
        
        if ($dateFrom) {
            $sql .= " AND DATE(o.created_at) >= ?";
            $params[] = $dateFrom;
            $types .= "s";
        }
        
        if ($dateTo) {
            $sql .= " AND DATE(o.created_at) <= ?";
            $params[] = $dateTo;
            $types .= "s";
        }
        
        // تجميع النتائج
        $sql .= " GROUP BY u.id ORDER BY total_spent DESC LIMIT 10";
        
        // تنفيذ الاستعلام
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $reportData[] = $row;
                $chartLabels[] = $row['name'];
                $chartData[] = $row['total_spent'];
            }
        }
        break;
}

// الحصول على إحصائيات عامة
$totalSales = 0;
$totalOrders = 0;
$averageOrderValue = 0;

$sql = "SELECT COUNT(id) as total_orders, SUM(total_amount) as total_sales 
        FROM orders 
        WHERE status = 'completed' AND payment_status = 'paid'";

// إضافة معايير التصفية
$params = [];
$types = "";

if ($storeId) {
    $sql .= " AND store_id = ?";
    $params[] = $storeId;
    $types .= "i";
}

if ($dateFrom) {
    $sql .= " AND DATE(created_at) >= ?";
    $params[] = $dateFrom;
    $types .= "s";
}

if ($dateTo) {
    $sql .= " AND DATE(created_at) <= ?";
    $params[] = $dateTo;
    $types .= "s";
}

// تنفيذ الاستعلام
$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $totalSales = $row['total_sales'] ?? 0;
    $totalOrders = $row['total_orders'] ?? 0;
    $averageOrderValue = $totalOrders > 0 ? $totalSales / $totalOrders : 0;
}

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">التقارير</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportBtn">
                            <i class="fas fa-file-export"></i> تصدير
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- فلتر التقارير -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">فلتر التقارير</h5>
                </div>
                <div class="card-body">
                    <form action="" method="GET">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="report_type" class="form-label">نوع التقرير</label>
                                <select name="report_type" id="report_type" class="form-select">
                                    <option value="sales" <?php echo $reportType === 'sales' ? 'selected' : ''; ?>>تقرير المبيعات</option>
                                    <option value="products" <?php echo $reportType === 'products' ? 'selected' : ''; ?>>المنتجات الأكثر مبيعاً</option>
                                    <option value="stores" <?php echo $reportType === 'stores' ? 'selected' : ''; ?>>المتاجر الأكثر مبيعاً</option>
                                    <option value="customers" <?php echo $reportType === 'customers' ? 'selected' : ''; ?>>العملاء الأكثر شراءً</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="store_id" class="form-label">المتجر</label>
                                <select name="store_id" id="store_id" class="form-select">
                                    <option value="">جميع المتاجر</option>
                                    <?php foreach ($stores as $store): ?>
                                        <option value="<?php echo $store['id']; ?>" <?php echo $storeId == $store['id'] ? 'selected' : ''; ?>>
                                            <?php echo $store['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3 sales-option" <?php echo $reportType !== 'sales' ? 'style="display:none;"' : ''; ?>>
                                <label for="group_by" class="form-label">تجميع حسب</label>
                                <select name="group_by" id="group_by" class="form-select">
                                    <option value="day" <?php echo $groupBy === 'day' ? 'selected' : ''; ?>>يوم</option>
                                    <option value="month" <?php echo $groupBy === 'month' ? 'selected' : ''; ?>>شهر</option>
                                    <option value="year" <?php echo $groupBy === 'year' ? 'selected' : ''; ?>>سنة</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" name="date_from" id="date_from" class="form-control" value="<?php echo $dateFrom; ?>">
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" name="date_to" id="date_to" class="form-control" value="<?php echo $dateTo; ?>">
                            </div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">عرض التقرير</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- ملخص التقرير -->
            <div class="row mb-4">
                <div class="col-md-4 mb-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">إجمالي المبيعات</h6>
                                    <h2 class="mb-0"><?php echo number_format($totalSales, 2); ?> ريال</h2>
                                </div>
                                <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">عدد الطلبات</h6>
                                    <h2 class="mb-0"><?php echo $totalOrders; ?></h2>
                                </div>
                                <i class="fas fa-shopping-cart fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">متوسط قيمة الطلب</h6>
                                    <h2 class="mb-0"><?php echo number_format($averageOrderValue, 2); ?> ريال</h2>
                                </div>
                                <i class="fas fa-calculator fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الرسم البياني للتقرير -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <?php
                        switch ($reportType) {
                            case 'sales':
                                echo 'تقرير المبيعات';
                                break;
                            case 'products':
                                echo 'المنتجات الأكثر مبيعاً';
                                break;
                            case 'stores':
                                echo 'المتاجر الأكثر مبيعاً';
                                break;
                            case 'customers':
                                echo 'العملاء الأكثر شراءً';
                                break;
                        }
                        ?>
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="reportChart" height="300"></canvas>
                </div>
            </div>
            
            <!-- بيانات التقرير -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">بيانات التقرير</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($reportData)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد بيانات تطابق معايير البحث.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <?php if ($reportType === 'sales'): ?>
                                            <th>التاريخ</th>
                                            <th>عدد الطلبات</th>
                                            <th>إجمالي المبيعات</th>
                                        <?php elseif ($reportType === 'products'): ?>
                                            <th>المنتج</th>
                                            <th>المتجر</th>
                                            <th>عدد الطلبات</th>
                                            <th>الكمية المباعة</th>
                                            <th>إجمالي المبيعات</th>
                                        <?php elseif ($reportType === 'stores'): ?>
                                            <th>المتجر</th>
                                            <th>عدد الطلبات</th>
                                            <th>إجمالي المبيعات</th>
                                        <?php elseif ($reportType === 'customers'): ?>
                                            <th>العميل</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>عدد الطلبات</th>
                                            <th>إجمالي المشتريات</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($reportData as $row): ?>
                                        <tr>
                                            <?php if ($reportType === 'sales'): ?>
                                                <td><?php echo $row['date']; ?></td>
                                                <td><?php echo $row['order_count']; ?></td>
                                                <td><?php echo number_format($row['total_sales'], 2); ?> ريال</td>
                                            <?php elseif ($reportType === 'products'): ?>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo APP_URL; ?>/public/uploads/products/<?php echo $row['image']; ?>" alt="<?php echo $row['name']; ?>" class="img-thumbnail me-2" width="50">
                                                        <?php echo $row['name']; ?>
                                                    </div>
                                                </td>
                                                <td><?php echo $row['store_name']; ?></td>
                                                <td><?php echo $row['order_count']; ?></td>
                                                <td><?php echo $row['total_quantity']; ?></td>
                                                <td><?php echo number_format($row['total_sales'], 2); ?> ريال</td>
                                            <?php elseif ($reportType === 'stores'): ?>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo APP_URL; ?>/public/uploads/stores/<?php echo $row['logo']; ?>" alt="<?php echo $row['name']; ?>" class="img-thumbnail me-2" width="50">
                                                        <?php echo $row['name']; ?>
                                                    </div>
                                                </td>
                                                <td><?php echo $row['order_count']; ?></td>
                                                <td><?php echo number_format($row['total_sales'], 2); ?> ريال</td>
                                            <?php elseif ($reportType === 'customers'): ?>
                                                <td><?php echo $row['name']; ?></td>
                                                <td><?php echo $row['email']; ?></td>
                                                <td><?php echo $row['order_count']; ?></td>
                                                <td><?php echo number_format($row['total_spent'], 2); ?> ريال</td>
                                            <?php endif; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript لإنشاء الرسوم البيانية وتصدير البيانات -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تغيير خيارات الفلتر حسب نوع التقرير
    document.getElementById('report_type').addEventListener('change', function() {
        const salesOptions = document.querySelectorAll('.sales-option');
        if (this.value === 'sales') {
            salesOptions.forEach(option => option.style.display = 'block');
        } else {
            salesOptions.forEach(option => option.style.display = 'none');
        }
    });
    
    // إنشاء الرسم البياني
    const reportCtx = document.getElementById('reportChart').getContext('2d');
    const chartType = '<?php echo $reportType === 'sales' ? 'line' : 'bar'; ?>';
    const chartTitle = '<?php 
        switch ($reportType) {
            case 'sales':
                echo 'المبيعات';
                break;
            case 'products':
                echo 'الكمية المباعة';
                break;
            case 'stores':
                echo 'إجمالي المبيعات';
                break;
            case 'customers':
                echo 'إجمالي المشتريات';
                break;
        }
    ?>';
    
    const reportChart = new Chart(reportCtx, {
        type: chartType,
        data: {
            labels: <?php echo json_encode($chartLabels); ?>,
            datasets: [{
                label: chartTitle,
                data: <?php echo json_encode($chartData); ?>,
                backgroundColor: 'rgba(13, 110, 253, 0.7)',
                borderColor: 'rgba(13, 110, 253, 1)',
                borderWidth: 1,
                fill: <?php echo $reportType === 'sales' ? 'true' : 'false'; ?>
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // تصدير البيانات
    document.getElementById('exportBtn').addEventListener('click', function() {
        // إنشاء CSV
        let csv = '';
        
        // إضافة عناوين الأعمدة
        <?php if ($reportType === 'sales'): ?>
            csv = 'التاريخ,عدد الطلبات,إجمالي المبيعات\n';
            <?php foreach ($reportData as $row): ?>
                csv += '<?php echo $row['date']; ?>,';
                csv += '<?php echo $row['order_count']; ?>,';
                csv += '<?php echo $row['total_sales']; ?>\n';
            <?php endforeach; ?>
        <?php elseif ($reportType === 'products'): ?>
            csv = 'المنتج,المتجر,عدد الطلبات,الكمية المباعة,إجمالي المبيعات\n';
            <?php foreach ($reportData as $row): ?>
                csv += '"<?php echo $row['name']; ?>",';
                csv += '"<?php echo $row['store_name']; ?>",';
                csv += '<?php echo $row['order_count']; ?>,';
                csv += '<?php echo $row['total_quantity']; ?>,';
                csv += '<?php echo $row['total_sales']; ?>\n';
            <?php endforeach; ?>
        <?php elseif ($reportType === 'stores'): ?>
            csv = 'المتجر,عدد الطلبات,إجمالي المبيعات\n';
            <?php foreach ($reportData as $row): ?>
                csv += '"<?php echo $row['name']; ?>",';
                csv += '<?php echo $row['order_count']; ?>,';
                csv += '<?php echo $row['total_sales']; ?>\n';
            <?php endforeach; ?>
        <?php elseif ($reportType === 'customers'): ?>
            csv = 'العميل,البريد الإلكتروني,عدد الطلبات,إجمالي المشتريات\n';
            <?php foreach ($reportData as $row): ?>
                csv += '"<?php echo $row['name']; ?>",';
                csv += '"<?php echo $row['email']; ?>",';
                csv += '<?php echo $row['order_count']; ?>,';
                csv += '<?php echo $row['total_spent']; ?>\n';
            <?php endforeach; ?>
        <?php endif; ?>
        
        // إنشاء رابط التنزيل
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', 'report_<?php echo $reportType; ?>_<?php echo date('Y-m-d'); ?>.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
});
</script>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
