<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelo de usuario
require_once '../models/User.php';

// Inicializar el modelo de usuario con la conexión a la base de datos
$userModel = new User($conn);

// Verificar si el usuario ya está logueado
if(isLoggedIn()) {
    // Redirigir según el rol
    if(isAdmin()) {
        redirect(APP_URL . '/admin/dashboard.php');
    } elseif(isVendor()) {
        redirect(APP_URL . '/vendor/dashboard.php');
    } else {
        redirect(APP_URL . '/public/index.php');
    }
}

// Procesar el formulario de registro
if($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar token CSRF
    if(!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'Error de validación. Por favor, intente nuevamente.';
        redirect(APP_URL . '/public/register.php');
    }
    
    // Obtener datos del formulario
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $phone = sanitize($_POST['phone']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validar campos
    $errors = [];
    
    if(empty($name)) {
        $errors[] = 'El nombre es obligatorio';
    }
    
    if(empty($email)) {
        $errors[] = 'El correo electrónico es obligatorio';
    } elseif(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'El correo electrónico no es válido';
    } elseif($userModel->emailExists($email)) {
        $errors[] = 'El correo electrónico ya está registrado';
    }
    
    if(empty($phone)) {
        $errors[] = 'El número de teléfono es obligatorio';
    }
    
    if(empty($password)) {
        $errors[] = 'La contraseña es obligatoria';
    } elseif(strlen($password) < 6) {
        $errors[] = 'La contraseña debe tener al menos 6 caracteres';
    }
    
    if($password !== $confirm_password) {
        $errors[] = 'Las contraseñas no coinciden';
    }
    
    // Si no hay errores, registrar al usuario
    if(empty($errors)) {
        // Configurar datos del usuario
        $userModel->name = $name;
        $userModel->email = $email;
        $userModel->phone = $phone;
        $userModel->password = $password;
        $userModel->role = 'customer'; // Rol por defecto para registro público
        $userModel->is_active = 1; // Activar cuenta automáticamente
        
        // Registrar usuario
        if($userModel->register()) {
            $_SESSION['success'] = 'تم إنشاء الحساب بنجاح. يمكنك الآن تسجيل الدخول.';
            redirect(APP_URL . '/public/login.php');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">إنشاء حساب جديد</h4>
            </div>
            <div class="card-body">
                <form action="<?php echo APP_URL; ?>/public/register.php" method="POST">
                    <!-- CSRF Token -->
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <small class="text-muted">يجب أن تتكون كلمة المرور من 6 أحرف على الأقل</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">إنشاء حساب</button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">لديك حساب بالفعل؟ <a href="<?php echo APP_URL; ?>/public/login.php">تسجيل الدخول</a></p>
                <p class="mt-2 mb-0">هل تريد الانضمام كبائع؟ <a href="<?php echo APP_URL; ?>/public/vendor-register.php">تسجيل كبائع</a></p>
            </div>
        </div>
    </div>
</div>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
