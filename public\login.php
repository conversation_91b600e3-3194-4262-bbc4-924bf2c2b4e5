<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelo de usuario
require_once '../models/User.php';

// Inicializar el modelo de usuario con la conexión a la base de datos
$userModel = new User($conn);

// Verificar si el usuario ya está logueado
if(isLoggedIn()) {
    // Redirigir según el rol
    if(isAdmin()) {
        redirect(APP_URL . '/admin/dashboard.php');
    } elseif(isVendor()) {
        redirect(APP_URL . '/vendor/dashboard.php');
    } else {
        redirect(APP_URL . '/public/index.php');
    }
}

// Procesar el formulario de inicio de sesión
if($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar token CSRF
    if(!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'Error de validación. Por favor, intente nuevamente.';
        redirect(APP_URL . '/public/login.php');
    }

    // Obtener datos del formulario
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];

    // Validar campos
    $errors = [];

    if(empty($email)) {
        $errors[] = 'El correo electrónico es obligatorio';
    }

    if(empty($password)) {
        $errors[] = 'La contraseña es obligatoria';
    }

    // Si no hay errores, intentar iniciar sesión
    if(empty($errors)) {
        $result = $userModel->login($email, $password);

        if(isset($result['error'])) {
            $_SESSION['error'] = $result['error'];
        } else {
            // Iniciar sesión
            $_SESSION['user_id'] = $result['id'];
            $_SESSION['user_name'] = $result['name'];
            $_SESSION['user_email'] = $result['email'];
            $_SESSION['user_role'] = $result['role'];
            $_SESSION['profile_image'] = isset($result['profile_image']) ? $result['profile_image'] : 'default.png';

            // Redirigir según el rol
            if($result['role'] === 'admin') {
                redirect(APP_URL . '/admin/dashboard.php');
            } elseif($result['role'] === 'vendor') {
                redirect(APP_URL . '/vendor/dashboard.php');
            } else {
                redirect(APP_URL . '/public/index.php');
            }
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">تسجيل الدخول</h4>
            </div>
            <div class="card-body">
                <form action="<?php echo APP_URL; ?>/public/login.php" method="POST">
                    <!-- CSRF Token -->
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">ليس لديك حساب؟ <a href="<?php echo APP_URL; ?>/public/register.php">إنشاء حساب جديد</a></p>
                <p class="mt-2 mb-0">هل تريد الانضمام كبائع؟ <a href="<?php echo APP_URL; ?>/public/vendor-register.php">تسجيل كبائع</a></p>
            </div>
        </div>
    </div>
</div>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
