<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تحديد ملف الإعدادات
$settingsFile = '../config/settings.php';

// التحقق من وجود ملف الإعدادات وإنشائه إذا لم يكن موجوداً
if (!file_exists($settingsFile)) {
    $defaultSettings = [
        'site_name' => 'ذوق ماركت',
        'site_description' => 'منصة متكاملة للمطاعم والسوبرماركت مع خدمات الدفع والتوصيل',
        'site_email' => '<EMAIL>',
        'site_phone' => '*********',
        'site_address' => 'المملكة العربية السعودية',
        'site_logo' => 'logo.png',
        'site_favicon' => 'favicon.ico',
        'social_facebook' => 'https://facebook.com/zoukmarket',
        'social_twitter' => 'https://twitter.com/zoukmarket',
        'social_instagram' => 'https://instagram.com/zoukmarket',
        'currency' => 'ريال',
        'currency_symbol' => 'ر.س',
        'tax_percentage' => 15,
        'delivery_fee' => 10,
        'min_order_amount' => 50,
        'about_us' => 'ذوق ماركت هو نظام شامل لاستضافة وإدارة مطاعم وسوبرماركت مع خدمات الدفع والتوصيل.',
        'privacy_policy' => 'سياسة الخصوصية لذوق ماركت...',
        'terms_conditions' => 'الشروط والأحكام لذوق ماركت...',
        'maintenance_mode' => false,
        'allow_registration' => true,
        'auto_approve_vendors' => false,
        'auto_approve_products' => false,
        'notification_email' => true,
        'notification_sms' => false,
        'notification_push' => false,
    ];
    
    // حفظ الإعدادات الافتراضية
    file_put_contents($settingsFile, '<?php return ' . var_export($defaultSettings, true) . ';');
}

// قراءة الإعدادات الحالية
$settings = include($settingsFile);

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/admin/settings.php');
    }
    
    // تحديد نوع الإعدادات المراد تحديثها
    $settingType = $_POST['setting_type'] ?? '';
    
    // تحديث الإعدادات حسب النوع
    switch ($settingType) {
        case 'general':
            $settings['site_name'] = sanitize($_POST['site_name']);
            $settings['site_description'] = sanitize($_POST['site_description']);
            $settings['site_email'] = sanitize($_POST['site_email']);
            $settings['site_phone'] = sanitize($_POST['site_phone']);
            $settings['site_address'] = sanitize($_POST['site_address']);
            break;
            
        case 'appearance':
            // معالجة تحميل الشعار
            if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === 0) {
                $logoName = 'logo_' . time() . '.' . pathinfo($_FILES['site_logo']['name'], PATHINFO_EXTENSION);
                $logoPath = PUBLIC_PATH . '/uploads/' . $logoName;
                
                if (move_uploaded_file($_FILES['site_logo']['tmp_name'], $logoPath)) {
                    $settings['site_logo'] = $logoName;
                }
            }
            
            // معالجة تحميل الأيقونة المفضلة
            if (isset($_FILES['site_favicon']) && $_FILES['site_favicon']['error'] === 0) {
                $faviconName = 'favicon_' . time() . '.' . pathinfo($_FILES['site_favicon']['name'], PATHINFO_EXTENSION);
                $faviconPath = PUBLIC_PATH . '/uploads/' . $faviconName;
                
                if (move_uploaded_file($_FILES['site_favicon']['tmp_name'], $faviconPath)) {
                    $settings['site_favicon'] = $faviconName;
                }
            }
            break;
            
        case 'social':
            $settings['social_facebook'] = sanitize($_POST['social_facebook']);
            $settings['social_twitter'] = sanitize($_POST['social_twitter']);
            $settings['social_instagram'] = sanitize($_POST['social_instagram']);
            break;
            
        case 'payment':
            $settings['currency'] = sanitize($_POST['currency']);
            $settings['currency_symbol'] = sanitize($_POST['currency_symbol']);
            $settings['tax_percentage'] = floatval($_POST['tax_percentage']);
            $settings['delivery_fee'] = floatval($_POST['delivery_fee']);
            $settings['min_order_amount'] = floatval($_POST['min_order_amount']);
            break;
            
        case 'content':
            $settings['about_us'] = $_POST['about_us'];
            $settings['privacy_policy'] = $_POST['privacy_policy'];
            $settings['terms_conditions'] = $_POST['terms_conditions'];
            break;
            
        case 'system':
            $settings['maintenance_mode'] = isset($_POST['maintenance_mode']);
            $settings['allow_registration'] = isset($_POST['allow_registration']);
            $settings['auto_approve_vendors'] = isset($_POST['auto_approve_vendors']);
            $settings['auto_approve_products'] = isset($_POST['auto_approve_products']);
            $settings['notification_email'] = isset($_POST['notification_email']);
            $settings['notification_sms'] = isset($_POST['notification_sms']);
            $settings['notification_push'] = isset($_POST['notification_push']);
            break;
    }
    
    // حفظ الإعدادات المحدثة
    file_put_contents($settingsFile, '<?php return ' . var_export($settings, true) . ';');
    
    $_SESSION['success'] = 'تم تحديث الإعدادات بنجاح';
    redirect(APP_URL . '/admin/settings.php?tab=' . $settingType);
}

// تحديد التبويب النشط
$activeTab = $_GET['tab'] ?? 'general';

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إعدادات النظام</h1>
            </div>
            
            <!-- تبويبات الإعدادات -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link <?php echo $activeTab === 'general' ? 'active' : ''; ?>" href="?tab=general">
                        <i class="fas fa-cog"></i> إعدادات عامة
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $activeTab === 'appearance' ? 'active' : ''; ?>" href="?tab=appearance">
                        <i class="fas fa-palette"></i> المظهر
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $activeTab === 'social' ? 'active' : ''; ?>" href="?tab=social">
                        <i class="fas fa-share-alt"></i> وسائل التواصل
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $activeTab === 'payment' ? 'active' : ''; ?>" href="?tab=payment">
                        <i class="fas fa-money-bill-wave"></i> الدفع والتوصيل
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $activeTab === 'content' ? 'active' : ''; ?>" href="?tab=content">
                        <i class="fas fa-file-alt"></i> المحتوى
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $activeTab === 'system' ? 'active' : ''; ?>" href="?tab=system">
                        <i class="fas fa-server"></i> النظام
                    </a>
                </li>
            </ul>
            
            <!-- نموذج الإعدادات -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <?php if ($activeTab === 'general'): ?>
                        <!-- الإعدادات العامة -->
                        <form action="" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="setting_type" value="general">
                            
                            <div class="mb-3">
                                <label for="site_name" class="form-label">اسم الموقع</label>
                                <input type="text" class="form-control" id="site_name" name="site_name" value="<?php echo $settings['site_name']; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_description" class="form-label">وصف الموقع</label>
                                <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo $settings['site_description']; ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="site_email" name="site_email" value="<?php echo $settings['site_email']; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="site_phone" name="site_phone" value="<?php echo $settings['site_phone']; ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="site_address" name="site_address" rows="2"><?php echo $settings['site_address']; ?></textarea>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                            </div>
                        </form>
                    <?php elseif ($activeTab === 'appearance'): ?>
                        <!-- إعدادات المظهر -->
                        <form action="" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="setting_type" value="appearance">
                            
                            <div class="mb-3">
                                <label for="site_logo" class="form-label">شعار الموقع</label>
                                <div class="mb-2">
                                    <?php if (!empty($settings['site_logo'])): ?>
                                        <img src="<?php echo APP_URL; ?>/public/uploads/<?php echo $settings['site_logo']; ?>" alt="الشعار الحالي" class="img-thumbnail" style="max-height: 100px;">
                                    <?php endif; ?>
                                </div>
                                <input type="file" class="form-control" id="site_logo" name="site_logo" accept="image/*">
                                <div class="form-text">الحجم الموصى به: 200×50 بكسل</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_favicon" class="form-label">أيقونة الموقع المفضلة</label>
                                <div class="mb-2">
                                    <?php if (!empty($settings['site_favicon'])): ?>
                                        <img src="<?php echo APP_URL; ?>/public/uploads/<?php echo $settings['site_favicon']; ?>" alt="الأيقونة الحالية" class="img-thumbnail" style="max-height: 32px;">
                                    <?php endif; ?>
                                </div>
                                <input type="file" class="form-control" id="site_favicon" name="site_favicon" accept="image/x-icon,image/png">
                                <div class="form-text">الحجم الموصى به: 32×32 بكسل</div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                            </div>
                        </form>
                    <?php elseif ($activeTab === 'social'): ?>
                        <!-- إعدادات وسائل التواصل الاجتماعي -->
                        <form action="" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="setting_type" value="social">
                            
                            <div class="mb-3">
                                <label for="social_facebook" class="form-label">فيسبوك</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-facebook"></i></span>
                                    <input type="url" class="form-control" id="social_facebook" name="social_facebook" value="<?php echo $settings['social_facebook']; ?>">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="social_twitter" class="form-label">تويتر</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-twitter"></i></span>
                                    <input type="url" class="form-control" id="social_twitter" name="social_twitter" value="<?php echo $settings['social_twitter']; ?>">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="social_instagram" class="form-label">انستغرام</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fab fa-instagram"></i></span>
                                    <input type="url" class="form-control" id="social_instagram" name="social_instagram" value="<?php echo $settings['social_instagram']; ?>">
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                            </div>
                        </form>
                    <?php elseif ($activeTab === 'payment'): ?>
                        <!-- إعدادات الدفع والتوصيل -->
                        <form action="" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="setting_type" value="payment">
                            
                            <div class="mb-3">
                                <label for="currency" class="form-label">العملة</label>
                                <input type="text" class="form-control" id="currency" name="currency" value="<?php echo $settings['currency']; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="currency_symbol" class="form-label">رمز العملة</label>
                                <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" value="<?php echo $settings['currency_symbol']; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="tax_percentage" class="form-label">نسبة الضريبة (%)</label>
                                <input type="number" class="form-control" id="tax_percentage" name="tax_percentage" value="<?php echo $settings['tax_percentage']; ?>" min="0" max="100" step="0.01" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="delivery_fee" class="form-label">رسوم التوصيل</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="delivery_fee" name="delivery_fee" value="<?php echo $settings['delivery_fee']; ?>" min="0" step="0.01" required>
                                    <span class="input-group-text"><?php echo $settings['currency_symbol']; ?></span>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="min_order_amount" class="form-label">الحد الأدنى للطلب</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="min_order_amount" name="min_order_amount" value="<?php echo $settings['min_order_amount']; ?>" min="0" step="0.01" required>
                                    <span class="input-group-text"><?php echo $settings['currency_symbol']; ?></span>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                            </div>
                        </form>
                    <?php elseif ($activeTab === 'content'): ?>
                        <!-- إعدادات المحتوى -->
                        <form action="" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="setting_type" value="content">
                            
                            <div class="mb-3">
                                <label for="about_us" class="form-label">من نحن</label>
                                <textarea class="form-control" id="about_us" name="about_us" rows="5"><?php echo $settings['about_us']; ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="privacy_policy" class="form-label">سياسة الخصوصية</label>
                                <textarea class="form-control" id="privacy_policy" name="privacy_policy" rows="5"><?php echo $settings['privacy_policy']; ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="terms_conditions" class="form-label">الشروط والأحكام</label>
                                <textarea class="form-control" id="terms_conditions" name="terms_conditions" rows="5"><?php echo $settings['terms_conditions']; ?></textarea>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                            </div>
                        </form>
                    <?php elseif ($activeTab === 'system'): ?>
                        <!-- إعدادات النظام -->
                        <form action="" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="setting_type" value="system">
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" <?php echo $settings['maintenance_mode'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="maintenance_mode">وضع الصيانة</label>
                                </div>
                                <div class="form-text">عند تفعيل وضع الصيانة، سيتم عرض صفحة الصيانة للزوار</div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="allow_registration" name="allow_registration" <?php echo $settings['allow_registration'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="allow_registration">السماح بالتسجيل</label>
                                </div>
                                <div class="form-text">السماح للمستخدمين بإنشاء حسابات جديدة</div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="auto_approve_vendors" name="auto_approve_vendors" <?php echo $settings['auto_approve_vendors'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="auto_approve_vendors">الموافقة التلقائية على البائعين</label>
                                </div>
                                <div class="form-text">الموافقة تلقائياً على طلبات تسجيل البائعين الجدد</div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="auto_approve_products" name="auto_approve_products" <?php echo $settings['auto_approve_products'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="auto_approve_products">الموافقة التلقائية على المنتجات</label>
                                </div>
                                <div class="form-text">الموافقة تلقائياً على المنتجات الجديدة المضافة من قبل البائعين</div>
                            </div>
                            
                            <h5 class="mt-4 mb-3">إعدادات الإشعارات</h5>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="notification_email" name="notification_email" <?php echo $settings['notification_email'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="notification_email">إشعارات البريد الإلكتروني</label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="notification_sms" name="notification_sms" <?php echo $settings['notification_sms'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="notification_sms">إشعارات الرسائل النصية</label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="notification_push" name="notification_push" <?php echo $settings['notification_push'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="notification_push">الإشعارات الفورية</label>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript لمحرر النصوص -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة محرر النصوص للمحتوى
    if (typeof ClassicEditor !== 'undefined') {
        const textareas = ['about_us', 'privacy_policy', 'terms_conditions'];
        
        textareas.forEach(id => {
            const textarea = document.getElementById(id);
            if (textarea) {
                ClassicEditor
                    .create(textarea, {
                        language: 'ar',
                        toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', '|', 'outdent', 'indent', '|', 'blockQuote', 'insertTable', 'undo', 'redo']
                    })
                    .catch(error => {
                        console.error(error);
                    });
            }
        });
    }
});
</script>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
