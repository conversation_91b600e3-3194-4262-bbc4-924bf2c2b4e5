<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المتجر
require_once '../models/Store.php';
require_once '../models/Product.php';

// التحقق من وجود معرف المتجر
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف المتجر غير صالح';
    redirect(APP_URL . '/admin/stores.php');
}

$storeId = intval($_GET['id']);

// إنشاء كائن المتجر
$storeModel = new Store($conn);
$productModel = new Product($conn);

// الحصول على بيانات المتجر
$store = $storeModel->getById($storeId);

if (!$store) {
    $_SESSION['error'] = 'المتجر غير موجود';
    redirect(APP_URL . '/admin/stores.php');
}

// الحصول على بيانات المالك
$sql = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $store['user_id']);
$stmt->execute();
$owner = $stmt->get_result()->fetch_assoc();

// الحصول على منتجات المتجر
$products = $productModel->getByStoreId($storeId, false);

// الحصول على إحصائيات المتجر
$sql = "SELECT COUNT(*) as total_orders, SUM(total_amount) as total_sales FROM orders WHERE store_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $storeId);
$stmt->execute();
$stats = $stmt->get_result()->fetch_assoc();

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تفاصيل المتجر</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="<?php echo APP_URL; ?>/admin/stores.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> العودة إلى قائمة المتاجر
                        </a>
                        <a href="<?php echo APP_URL; ?>/admin/edit_store.php?id=<?php echo $storeId; ?>" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i> تعديل المتجر
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- معلومات المتجر -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">معلومات المتجر</h5>
                        </div>
                        <div class="card-body text-center">
                            <img src="<?php echo APP_URL; ?>/public/uploads/stores/<?php echo $store['logo']; ?>" alt="<?php echo $store['name']; ?>" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                            <h4><?php echo $store['name']; ?></h4>
                            <p class="text-muted"><?php echo $store['category_name']; ?></p>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>الحالة:</span>
                                <?php if ($store['is_active']): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير نشط</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>مميز:</span>
                                <?php if ($store['is_featured']): ?>
                                    <span class="badge bg-warning text-dark">نعم</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">لا</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>تاريخ الإنشاء:</span>
                                <span><?php echo date('Y-m-d', strtotime($store['created_at'])); ?></span>
                            </div>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>آخر تحديث:</span>
                                <span><?php echo date('Y-m-d', strtotime($store['updated_at'])); ?></span>
                            </div>
                            
                            <hr>
                            
                            <div class="text-start">
                                <p><i class="fas fa-map-marker-alt me-2"></i> <?php echo $store['address']; ?></p>
                                <p><i class="fas fa-phone me-2"></i> <?php echo $store['phone']; ?></p>
                            </div>
                            
                            <hr>
                            
                            <div class="d-grid gap-2">
                                <?php if ($store['is_active']): ?>
                                    <a href="<?php echo APP_URL; ?>/admin/stores.php?action=deactivate&id=<?php echo $storeId; ?>" class="btn btn-warning" onclick="return confirm('هل أنت متأكد من تعطيل هذا المتجر؟')">
                                        <i class="fas fa-ban"></i> تعطيل المتجر
                                    </a>
                                <?php else: ?>
                                    <a href="<?php echo APP_URL; ?>/admin/stores.php?action=activate&id=<?php echo $storeId; ?>" class="btn btn-success" onclick="return confirm('هل أنت متأكد من تفعيل هذا المتجر؟')">
                                        <i class="fas fa-check"></i> تفعيل المتجر
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($store['is_featured']): ?>
                                    <a href="<?php echo APP_URL; ?>/admin/stores.php?action=unfeature&id=<?php echo $storeId; ?>" class="btn btn-secondary" onclick="return confirm('هل أنت متأكد من إلغاء تمييز هذا المتجر؟')">
                                        <i class="fas fa-star-half-alt"></i> إلغاء التمييز
                                    </a>
                                <?php else: ?>
                                    <a href="<?php echo APP_URL; ?>/admin/stores.php?action=feature&id=<?php echo $storeId; ?>" class="btn btn-warning" onclick="return confirm('هل أنت متأكد من تمييز هذا المتجر؟')">
                                        <i class="fas fa-star"></i> تمييز المتجر
                                    </a>
                                <?php endif; ?>
                                
                                <a href="<?php echo APP_URL; ?>/admin/stores.php?action=delete&id=<?php echo $storeId; ?>" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المتجر؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                    <i class="fas fa-trash"></i> حذف المتجر
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="row">
                        <!-- معلومات المالك -->
                        <div class="col-md-12 mb-4">
                            <div class="card shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">معلومات المالك</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">الاسم</label>
                                                <p class="form-control"><?php echo $owner['name']; ?></p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">البريد الإلكتروني</label>
                                                <p class="form-control"><?php echo $owner['email']; ?></p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">رقم الهاتف</label>
                                                <p class="form-control"><?php echo $owner['phone']; ?></p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">حالة الحساب</label>
                                                <p class="form-control">
                                                    <?php if ($owner['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">غير نشط</span>
                                                    <?php endif; ?>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-grid">
                                        <a href="<?php echo APP_URL; ?>/admin/edit_user.php?id=<?php echo $owner['id']; ?>" class="btn btn-primary">
                                            <i class="fas fa-user-edit"></i> تعديل بيانات المالك
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- وصف المتجر -->
                        <div class="col-md-12 mb-4">
                            <div class="card shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">وصف المتجر</h5>
                                </div>
                                <div class="card-body">
                                    <p><?php echo $store['description']; ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- صورة الغلاف -->
                        <?php if ($store['banner']): ?>
                            <div class="col-md-12 mb-4">
                                <div class="card shadow-sm">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">صورة الغلاف</h5>
                                    </div>
                                    <div class="card-body p-0">
                                        <img src="<?php echo APP_URL; ?>/public/uploads/stores/<?php echo $store['banner']; ?>" alt="<?php echo $store['name']; ?>" class="img-fluid w-100">
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- إحصائيات المتجر -->
                        <div class="col-md-12 mb-4">
                            <div class="card shadow-sm">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">إحصائيات المتجر</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <div class="card bg-primary text-white">
                                                <div class="card-body">
                                                    <h5 class="card-title">عدد المنتجات</h5>
                                                    <h2 class="mb-0"><?php echo count($products); ?></h2>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card bg-success text-white">
                                                <div class="card-body">
                                                    <h5 class="card-title">عدد الطلبات</h5>
                                                    <h2 class="mb-0"><?php echo $stats['total_orders'] ?? 0; ?></h2>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card bg-info text-white">
                                                <div class="card-body">
                                                    <h5 class="card-title">إجمالي المبيعات</h5>
                                                    <h2 class="mb-0"><?php echo number_format($stats['total_sales'] ?? 0, 2); ?> ريال</h2>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- منتجات المتجر -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">منتجات المتجر</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($products)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد منتجات لهذا المتجر حتى الآن.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>الحالة</th>
                                        <th>مميز</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td><?php echo $product['id']; ?></td>
                                            <td>
                                                <img src="<?php echo APP_URL; ?>/public/uploads/products/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" class="img-thumbnail" width="50">
                                            </td>
                                            <td><?php echo $product['name']; ?></td>
                                            <td><?php echo $product['category_name']; ?></td>
                                            <td><?php echo number_format($product['price'], 2); ?> ريال</td>
                                            <td><?php echo $product['stock_quantity']; ?></td>
                                            <td>
                                                <?php if ($product['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($product['is_featured']): ?>
                                                    <span class="badge bg-warning text-dark">مميز</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير مميز</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo APP_URL; ?>/admin/product_details.php?id=<?php echo $product['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo APP_URL; ?>/admin/edit_product.php?id=<?php echo $product['id']; ?>" class="btn btn-sm btn-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
