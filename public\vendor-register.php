<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelos
require_once '../models/User.php';
require_once '../models/Store.php';

// Inicializar modelos
$userModel = new User($conn);
$storeModel = new Store($conn);

// Verificar si el usuario ya está logueado
if(isLoggedIn()) {
    // Si ya es vendedor, redirigir a su panel
    if(isVendor()) {
        redirect(APP_URL . '/vendor/dashboard.php');
    }
    // Si es cliente o admin, mostrar mensaje
    $_SESSION['error'] = 'Ya tienes una cuenta. Si deseas registrarte como vendedor, cierra sesión primero.';
    redirect(APP_URL . '/public/index.php');
}

// Obtener categorías de tiendas
$sql = "SELECT * FROM store_categories ORDER BY name";
$result = $conn->query($sql);
$categories = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
}

// Procesar el formulario de registro
if($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar token CSRF
    if(!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'Error de validación. Por favor, intente nuevamente.';
        redirect(APP_URL . '/public/vendor-register.php');
    }
    
    // Obtener datos del formulario
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $phone = sanitize($_POST['phone']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $store_name = sanitize($_POST['store_name']);
    $store_description = sanitize($_POST['store_description']);
    $store_address = sanitize($_POST['store_address']);
    $store_phone = sanitize($_POST['store_phone']);
    $category_id = intval($_POST['category_id']);
    
    // Validar campos
    $errors = [];
    
    // Validar usuario
    if(empty($name)) {
        $errors[] = 'El nombre es obligatorio';
    }
    
    if(empty($email)) {
        $errors[] = 'El correo electrónico es obligatorio';
    } elseif(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'El correo electrónico no es válido';
    } elseif($userModel->emailExists($email)) {
        $errors[] = 'El correo electrónico ya está registrado';
    }
    
    if(empty($phone)) {
        $errors[] = 'El número de teléfono es obligatorio';
    }
    
    if(empty($password)) {
        $errors[] = 'La contraseña es obligatoria';
    } elseif(strlen($password) < 6) {
        $errors[] = 'La contraseña debe tener al menos 6 caracteres';
    }
    
    if($password !== $confirm_password) {
        $errors[] = 'Las contraseñas no coinciden';
    }
    
    // Validar tienda
    if(empty($store_name)) {
        $errors[] = 'El nombre de la tienda es obligatorio';
    }
    
    if(empty($store_description)) {
        $errors[] = 'La descripción de la tienda es obligatoria';
    }
    
    if(empty($store_address)) {
        $errors[] = 'La dirección de la tienda es obligatoria';
    }
    
    if(empty($store_phone)) {
        $errors[] = 'El teléfono de la tienda es obligatorio';
    }
    
    if($category_id <= 0) {
        $errors[] = 'Debe seleccionar una categoría para la tienda';
    }
    
    // Validar logo
    if(!isset($_FILES['logo']) || $_FILES['logo']['error'] != 0) {
        $errors[] = 'Debe subir un logo para la tienda';
    } else {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB
        
        if(!in_array($_FILES['logo']['type'], $allowed_types)) {
            $errors[] = 'El logo debe ser una imagen (JPEG, PNG o GIF)';
        }
        
        if($_FILES['logo']['size'] > $max_size) {
            $errors[] = 'El logo no debe superar los 2MB';
        }
    }
    
    // Validar banner (opcional)
    if(isset($_FILES['banner']) && $_FILES['banner']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 5 * 1024 * 1024; // 5MB
        
        if(!in_array($_FILES['banner']['type'], $allowed_types)) {
            $errors[] = 'El banner debe ser una imagen (JPEG, PNG o GIF)';
        }
        
        if($_FILES['banner']['size'] > $max_size) {
            $errors[] = 'El banner no debe superar los 5MB';
        }
    }
    
    // Si no hay errores, registrar al usuario y la tienda
    if(empty($errors)) {
        // Crear directorio de uploads si no existe
        $uploads_dir = PUBLIC_PATH . '/uploads/stores';
        if(!is_dir($uploads_dir)) {
            mkdir($uploads_dir, 0755, true);
        }
        
        // Procesar logo
        $logo_filename = uniqid() . '_' . $_FILES['logo']['name'];
        $logo_path = $uploads_dir . '/' . $logo_filename;
        move_uploaded_file($_FILES['logo']['tmp_name'], $logo_path);
        
        // Procesar banner (si existe)
        $banner_filename = null;
        if(isset($_FILES['banner']) && $_FILES['banner']['error'] == 0) {
            $banner_filename = uniqid() . '_' . $_FILES['banner']['name'];
            $banner_path = $uploads_dir . '/' . $banner_filename;
            move_uploaded_file($_FILES['banner']['tmp_name'], $banner_path);
        }
        
        // Configurar datos del usuario
        $userModel->name = $name;
        $userModel->email = $email;
        $userModel->phone = $phone;
        $userModel->password = $password;
        $userModel->role = 'vendor'; // Rol de vendedor
        $userModel->is_active = 1; // Activar cuenta automáticamente
        
        // Registrar usuario
        if($userModel->register()) {
            // Configurar datos de la tienda
            $storeModel->user_id = $userModel->id;
            $storeModel->name = $store_name;
            $storeModel->description = $store_description;
            $storeModel->logo = $logo_filename;
            $storeModel->banner = $banner_filename;
            $storeModel->address = $store_address;
            $storeModel->phone = $store_phone;
            $storeModel->category_id = $category_id;
            $storeModel->is_active = 0; // Pendiente de aprobación
            $storeModel->is_featured = 0;
            
            // Registrar tienda
            if($storeModel->create()) {
                $_SESSION['success'] = 'تم إنشاء حساب البائع بنجاح. سيتم مراجعة طلبك من قبل الإدارة وتفعيله قريباً.';
                redirect(APP_URL . '/public/login.php');
            } else {
                // Si falla la creación de la tienda, eliminar el usuario
                $userModel->delete($userModel->id);
                $_SESSION['error'] = 'حدث خطأ أثناء إنشاء المتجر. يرجى المحاولة مرة أخرى.';
            }
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">تسجيل كبائع</h4>
            </div>
            <div class="card-body">
                <form action="<?php echo APP_URL; ?>/public/vendor-register.php" method="POST" enctype="multipart/form-data">
                    <!-- CSRF Token -->
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <h5 class="mb-3">معلومات الحساب</h5>
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <small class="text-muted">يجب أن تتكون كلمة المرور من 6 أحرف على الأقل</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h5 class="mb-3">معلومات المتجر</h5>
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <label for="store_name" class="form-label">اسم المتجر</label>
                            <input type="text" class="form-control" id="store_name" name="store_name" value="<?php echo isset($_POST['store_name']) ? htmlspecialchars($_POST['store_name']) : ''; ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">فئة المتجر</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">اختر الفئة</option>
                                <?php foreach($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                        <?php echo $category['name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="store_description" class="form-label">وصف المتجر</label>
                            <textarea class="form-control" id="store_description" name="store_description" rows="3" required><?php echo isset($_POST['store_description']) ? htmlspecialchars($_POST['store_description']) : ''; ?></textarea>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="store_address" class="form-label">عنوان المتجر</label>
                            <input type="text" class="form-control" id="store_address" name="store_address" value="<?php echo isset($_POST['store_address']) ? htmlspecialchars($_POST['store_address']) : ''; ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="store_phone" class="form-label">هاتف المتجر</label>
                            <input type="tel" class="form-control" id="store_phone" name="store_phone" value="<?php echo isset($_POST['store_phone']) ? htmlspecialchars($_POST['store_phone']) : ''; ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="logo" class="form-label">شعار المتجر</label>
                            <input type="file" class="form-control" id="logo" name="logo" accept="image/*" required>
                            <small class="text-muted">الحد الأقصى: 2MB. الأنواع المسموحة: JPEG, PNG, GIF</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="banner" class="form-label">صورة غلاف المتجر (اختياري)</label>
                            <input type="file" class="form-control" id="banner" name="banner" accept="image/*">
                            <small class="text-muted">الحد الأقصى: 5MB. الأنواع المسموحة: JPEG, PNG, GIF</small>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <p class="mb-0"><i class="fas fa-info-circle"></i> بعد التسجيل، سيتم مراجعة طلبك من قبل الإدارة وتفعيل حسابك في أقرب وقت.</p>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">تسجيل كبائع</button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">لديك حساب بالفعل؟ <a href="<?php echo APP_URL; ?>/public/login.php">تسجيل الدخول</a></p>
            </div>
        </div>
    </div>
</div>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
