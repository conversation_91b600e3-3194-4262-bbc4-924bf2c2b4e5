<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج الطلب
require_once '../models/Order.php';
require_once '../models/Store.php';

// إنشاء كائن الطلب
$orderModel = new Order($conn);
$storeModel = new Store($conn);

// معالجة الإجراءات
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $orderId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    // التحقق من وجود الطلب
    $order = $orderModel->getById($orderId);
    
    if (!$order) {
        $_SESSION['error'] = 'الطلب غير موجود';
        redirect(APP_URL . '/admin/orders.php');
    }
    
    // تنفيذ الإجراء المطلوب
    switch ($action) {
        case 'update_status':
            if (isset($_GET['status'])) {
                $status = $_GET['status'];
                $validStatuses = ['new', 'processing', 'ready', 'delivering', 'completed', 'cancelled'];
                
                if (in_array($status, $validStatuses)) {
                    if ($orderModel->updateStatus($orderId, $status, $order['store_id'])) {
                        $_SESSION['success'] = 'تم تحديث حالة الطلب بنجاح';
                    } else {
                        $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة الطلب';
                    }
                } else {
                    $_SESSION['error'] = 'حالة الطلب غير صالحة';
                }
            } else {
                $_SESSION['error'] = 'لم يتم تحديد حالة الطلب';
            }
            break;
            
        case 'update_payment':
            if (isset($_GET['payment_status'])) {
                $paymentStatus = $_GET['payment_status'];
                $validPaymentStatuses = ['pending', 'paid', 'refunded'];
                
                if (in_array($paymentStatus, $validPaymentStatuses)) {
                    if ($orderModel->updatePaymentStatus($orderId, $paymentStatus)) {
                        $_SESSION['success'] = 'تم تحديث حالة الدفع بنجاح';
                    } else {
                        $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة الدفع';
                    }
                } else {
                    $_SESSION['error'] = 'حالة الدفع غير صالحة';
                }
            } else {
                $_SESSION['error'] = 'لم يتم تحديد حالة الدفع';
            }
            break;
    }
    
    redirect(APP_URL . '/admin/orders.php');
}

// الحصول على معايير التصفية
$storeId = isset($_GET['store_id']) ? intval($_GET['store_id']) : null;
$status = isset($_GET['status']) ? $_GET['status'] : null;
$paymentStatus = isset($_GET['payment_status']) ? $_GET['payment_status'] : null;
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : null;
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : null;

// الحصول على قائمة الطلبات
$sql = "SELECT o.*, s.name as store_name, u.name as customer_name 
        FROM orders o
        JOIN stores s ON o.store_id = s.id
        JOIN users u ON o.user_id = u.id
        WHERE 1=1";

// إضافة معايير التصفية
$params = [];
$types = "";

if ($storeId) {
    $sql .= " AND o.store_id = ?";
    $params[] = $storeId;
    $types .= "i";
}

if ($status) {
    $sql .= " AND o.status = ?";
    $params[] = $status;
    $types .= "s";
}

if ($paymentStatus) {
    $sql .= " AND o.payment_status = ?";
    $params[] = $paymentStatus;
    $types .= "s";
}

if ($dateFrom) {
    $sql .= " AND DATE(o.created_at) >= ?";
    $params[] = $dateFrom;
    $types .= "s";
}

if ($dateTo) {
    $sql .= " AND DATE(o.created_at) <= ?";
    $params[] = $dateTo;
    $types .= "s";
}

$sql .= " ORDER BY o.created_at DESC";

// تنفيذ الاستعلام
$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
$orders = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $orders[] = $row;
    }
}

// الحصول على قائمة المتاجر للفلتر
$stores = $storeModel->getAll(null, true);

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الطلبات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="exportBtn">
                            <i class="fas fa-file-export"></i> تصدير
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- فلتر الطلبات -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">فلتر الطلبات</h5>
                </div>
                <div class="card-body">
                    <form action="" method="GET">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="store_id" class="form-label">المتجر</label>
                                <select name="store_id" id="store_id" class="form-select">
                                    <option value="">جميع المتاجر</option>
                                    <?php foreach ($stores as $store): ?>
                                        <option value="<?php echo $store['id']; ?>" <?php echo $storeId == $store['id'] ? 'selected' : ''; ?>>
                                            <?php echo $store['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="status" class="form-label">حالة الطلب</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="new" <?php echo $status === 'new' ? 'selected' : ''; ?>>جديد</option>
                                    <option value="processing" <?php echo $status === 'processing' ? 'selected' : ''; ?>>قيد التجهيز</option>
                                    <option value="ready" <?php echo $status === 'ready' ? 'selected' : ''; ?>>جاهز</option>
                                    <option value="delivering" <?php echo $status === 'delivering' ? 'selected' : ''; ?>>قيد التوصيل</option>
                                    <option value="completed" <?php echo $status === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                                    <option value="cancelled" <?php echo $status === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="payment_status" class="form-label">حالة الدفع</label>
                                <select name="payment_status" id="payment_status" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" <?php echo $paymentStatus === 'pending' ? 'selected' : ''; ?>>معلق</option>
                                    <option value="paid" <?php echo $paymentStatus === 'paid' ? 'selected' : ''; ?>>مدفوع</option>
                                    <option value="refunded" <?php echo $paymentStatus === 'refunded' ? 'selected' : ''; ?>>مسترجع</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" name="date_from" id="date_from" class="form-control" value="<?php echo $dateFrom; ?>">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" name="date_to" id="date_to" class="form-control" value="<?php echo $dateTo; ?>">
                            </div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">تصفية</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- قائمة الطلبات -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">قائمة الطلبات</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($orders)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد طلبات تطابق معايير البحث.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>المتجر</th>
                                        <th>المبلغ</th>
                                        <th>طريقة الدفع</th>
                                        <th>حالة الدفع</th>
                                        <th>حالة الطلب</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orders as $order): ?>
                                        <tr>
                                            <td><?php echo $order['id']; ?></td>
                                            <td><?php echo $order['customer_name']; ?></td>
                                            <td><?php echo $order['store_name']; ?></td>
                                            <td><?php echo number_format($order['total_amount'], 2); ?> شيكل</td>
                                            <td>
                                                <?php
                                                switch ($order['payment_method']) {
                                                    case 'cash':
                                                        echo 'نقدي';
                                                        break;
                                                    case 'card':
                                                        echo 'بطاقة ائتمان';
                                                        break;
                                                    case 'wallet':
                                                        echo 'محفظة إلكترونية';
                                                        break;
                                                    default:
                                                        echo $order['payment_method'];
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $paymentStatusClass = '';
                                                $paymentStatusText = '';
                                                switch ($order['payment_status']) {
                                                    case 'pending':
                                                        $paymentStatusClass = 'bg-warning text-dark';
                                                        $paymentStatusText = 'معلق';
                                                        break;
                                                    case 'paid':
                                                        $paymentStatusClass = 'bg-success';
                                                        $paymentStatusText = 'مدفوع';
                                                        break;
                                                    case 'refunded':
                                                        $paymentStatusClass = 'bg-danger';
                                                        $paymentStatusText = 'مسترجع';
                                                        break;
                                                }
                                                echo '<span class="badge ' . $paymentStatusClass . '">' . $paymentStatusText . '</span>';
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusText = '';
                                                switch ($order['status']) {
                                                    case 'new':
                                                        $statusClass = 'bg-primary';
                                                        $statusText = 'جديد';
                                                        break;
                                                    case 'processing':
                                                        $statusClass = 'bg-warning text-dark';
                                                        $statusText = 'قيد التجهيز';
                                                        break;
                                                    case 'ready':
                                                        $statusClass = 'bg-info';
                                                        $statusText = 'جاهز';
                                                        break;
                                                    case 'delivering':
                                                        $statusClass = 'bg-info';
                                                        $statusText = 'قيد التوصيل';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'bg-success';
                                                        $statusText = 'مكتمل';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'bg-danger';
                                                        $statusText = 'ملغي';
                                                        break;
                                                }
                                                echo '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';
                                                ?>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo APP_URL; ?>/admin/order_details.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="تغيير الحالة">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><h6 class="dropdown-header">تغيير حالة الطلب</h6></li>
                                                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/orders.php?action=update_status&id=<?php echo $order['id']; ?>&status=new">جديد</a></li>
                                                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/orders.php?action=update_status&id=<?php echo $order['id']; ?>&status=processing">قيد التجهيز</a></li>
                                                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/orders.php?action=update_status&id=<?php echo $order['id']; ?>&status=ready">جاهز</a></li>
                                                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/orders.php?action=update_status&id=<?php echo $order['id']; ?>&status=delivering">قيد التوصيل</a></li>
                                                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/orders.php?action=update_status&id=<?php echo $order['id']; ?>&status=completed">مكتمل</a></li>
                                                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/orders.php?action=update_status&id=<?php echo $order['id']; ?>&status=cancelled">ملغي</a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><h6 class="dropdown-header">تغيير حالة الدفع</h6></li>
                                                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/orders.php?action=update_payment&id=<?php echo $order['id']; ?>&payment_status=pending">معلق</a></li>
                                                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/orders.php?action=update_payment&id=<?php echo $order['id']; ?>&payment_status=paid">مدفوع</a></li>
                                                        <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/admin/orders.php?action=update_payment&id=<?php echo $order['id']; ?>&payment_status=refunded">مسترجع</a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- إحصائيات الطلبات -->
            <div class="row mt-4">
                <div class="col-md-3 mb-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">إجمالي الطلبات</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM orders";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-shopping-cart fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">الطلبات المكتملة</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM orders WHERE status = 'completed'";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">الطلبات قيد التنفيذ</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM orders WHERE status IN ('new', 'processing', 'ready', 'delivering')";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-clock fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">الطلبات الملغاة</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM orders WHERE status = 'cancelled'";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-times-circle fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- رسم بياني للطلبات -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إحصائيات الطلبات</h5>
                </div>
                <div class="card-body">
                    <canvas id="ordersChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript لإنشاء الرسم البياني -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // بيانات إحصائيات الطلبات
    const ordersCtx = document.getElementById('ordersChart').getContext('2d');
    const ordersChart = new Chart(ordersCtx, {
        type: 'bar',
        data: {
            labels: ['جديد', 'قيد التجهيز', 'جاهز', 'قيد التوصيل', 'مكتمل', 'ملغي'],
            datasets: [{
                label: 'عدد الطلبات',
                data: [
                    <?php
                    $statuses = ['new', 'processing', 'ready', 'delivering', 'completed', 'cancelled'];
                    foreach ($statuses as $s) {
                        $sql = "SELECT COUNT(*) as count FROM orders WHERE status = '$s'";
                        $result = $conn->query($sql);
                        $row = $result->fetch_assoc();
                        echo $row['count'] . ', ';
                    }
                    ?>
                ],
                backgroundColor: [
                    'rgba(13, 110, 253, 0.7)',
                    'rgba(255, 193, 7, 0.7)',
                    'rgba(13, 202, 240, 0.7)',
                    'rgba(253, 126, 20, 0.7)',
                    'rgba(25, 135, 84, 0.7)',
                    'rgba(220, 53, 69, 0.7)'
                ],
                borderColor: [
                    'rgba(13, 110, 253, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(13, 202, 240, 1)',
                    'rgba(253, 126, 20, 1)',
                    'rgba(25, 135, 84, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
    
    // تصدير البيانات
    document.getElementById('exportBtn').addEventListener('click', function() {
        // إنشاء CSV
        let csv = 'رقم الطلب,العميل,المتجر,المبلغ,طريقة الدفع,حالة الدفع,حالة الطلب,تاريخ الطلب\n';
        
        <?php foreach ($orders as $order): ?>
            csv += '<?php echo $order['id']; ?>,';
            csv += '"<?php echo $order['customer_name']; ?>",';
            csv += '"<?php echo $order['store_name']; ?>",';
            csv += '<?php echo $order['total_amount']; ?>,';
            csv += '<?php echo $order['payment_method']; ?>,';
            csv += '<?php echo $order['payment_status']; ?>,';
            csv += '<?php echo $order['status']; ?>,';
            csv += '<?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?>\n';
        <?php endforeach; ?>
        
        // إنشاء رابط التنزيل
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', 'orders_export_<?php echo date('Y-m-d'); ?>.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });
});
</script>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
