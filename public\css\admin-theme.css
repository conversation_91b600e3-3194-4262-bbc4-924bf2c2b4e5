/* Zouk Market - Admin Dashboard Theme */

:root {
    /* Default Theme - Blue */
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4895ef;
    --success-color: #4cc9f0;
    --info-color: #4895ef;
    --warning-color: #f72585;
    --danger-color: #e63946;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    
    /* Text Colors */
    --text-dark: #212529;
    --text-light: #f8f9fa;
    --text-muted: #6c757d;
    
    /* Background Colors */
    --bg-sidebar: #212529;
    --bg-content: #f8f9fa;
    
    /* Card & Elements */
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --card-hover-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --border-radius: 0.375rem;
    --transition-speed: 0.3s;
}

/* Theme Variations */
body.theme-red {
    --primary-color: #e63946;
    --secondary-color: #d62828;
    --accent-color: #f94144;
    --success-color: #f3722c;
    --info-color: #f8961e;
    --warning-color: #f9844a;
    --danger-color: #f94144;
    --bg-sidebar: #2b2d42;
}

body.theme-green {
    --primary-color: #2a9d8f;
    --secondary-color: #168aad;
    --accent-color: #52b788;
    --success-color: #95d5b2;
    --info-color: #74c69d;
    --warning-color: #f8961e;
    --danger-color: #ef476f;
    --bg-sidebar: #1b4332;
}

body.theme-purple {
    --primary-color: #7209b7;
    --secondary-color: #560bad;
    --accent-color: #b5179e;
    --success-color: #4cc9f0;
    --info-color: #4895ef;
    --warning-color: #f72585;
    --danger-color: #e63946;
    --bg-sidebar: #240046;
}

body.theme-orange {
    --primary-color: #fb8500;
    --secondary-color: #ffb703;
    --accent-color: #fd9e02;
    --success-color: #06d6a0;
    --info-color: #118ab2;
    --warning-color: #ef476f;
    --danger-color: #e63946;
    --bg-sidebar: #023047;
}

/* General Styles */
body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--bg-content);
    color: var(--text-dark);
    transition: background-color var(--transition-speed) ease;
}

/* Sidebar Styles */
.dashboard-sidebar {
    background-color: var(--bg-sidebar);
    transition: background-color var(--transition-speed) ease;
    min-height: 100vh;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-heading {
    color: var(--text-light);
    padding: 1rem 1.5rem;
    font-size: 1.2rem;
    font-weight: 700;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
    padding: 1rem 0;
}

.sidebar-menu .nav-link {
    color: rgba(255, 255, 255, 0.7);
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    transition: all var(--transition-speed) ease;
    border-right: 3px solid transparent;
}

.sidebar-menu .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.05);
    border-right-color: var(--primary-color);
}

.sidebar-menu .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-right-color: var(--primary-color);
}

.sidebar-menu .nav-link i {
    margin-left: 0.75rem;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Card Styles */
.card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--card-shadow);
    transition: all var(--transition-speed) ease;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--card-hover-shadow);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Stats Cards */
.stats-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: all var(--transition-speed) ease;
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card .icon {
    font-size: 2.5rem;
    opacity: 0.2;
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card.primary {
    background-color: var(--primary-color);
    color: white;
}

.stats-card.success {
    background-color: var(--success-color);
    color: white;
}

.stats-card.warning {
    background-color: var(--warning-color);
    color: white;
}

.stats-card.danger {
    background-color: var(--danger-color);
    color: white;
}

.stats-card .card-footer {
    background-color: rgba(0, 0, 0, 0.1);
    border-top: none;
    padding: 0.75rem 1.5rem;
}

.stats-card .card-footer a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color var(--transition-speed) ease;
}

.stats-card .card-footer a:hover {
    color: white;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table thead th {
    border-top: none;
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
    color: var(--text-dark);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) ease;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.4em 0.6em;
    border-radius: 0.25rem;
}

/* Charts */
canvas {
    max-width: 100%;
}

/* Theme Switcher */
.theme-switcher {
    position: fixed;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    background-color: white;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    transition: transform var(--transition-speed) ease;
}

.theme-switcher.collapsed {
    transform: translateY(-50%) translateX(-100%);
}

.theme-switcher .toggle-btn {
    position: absolute;
    top: 50%;
    right: -15px;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-dark);
    font-size: 0.8rem;
}

.theme-option {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    transition: transform var(--transition-speed) ease;
    border: 2px solid transparent;
}

.theme-option:hover {
    transform: scale(1.1);
}

.theme-option.active {
    border-color: var(--text-dark);
}

.theme-blue {
    background-color: #4361ee;
}

.theme-red {
    background-color: #e63946;
}

.theme-green {
    background-color: #2a9d8f;
}

.theme-purple {
    background-color: #7209b7;
}

.theme-orange {
    background-color: #fb8500;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn var(--transition-speed) ease;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .dashboard-sidebar {
        position: fixed;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        z-index: 1030;
        transform: translateX(100%);
        transition: transform var(--transition-speed) ease;
    }
    
    .dashboard-sidebar.show {
        transform: translateX(0);
    }
    
    .sidebar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1020;
        display: none;
    }
    
    .sidebar-backdrop.show {
        display: block;
    }
}
