<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول ودور المستخدم
if (!isLoggedIn() || !isVendor()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كبائع للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// تضمين النماذج المطلوبة
require_once '../models/Store.php';
require_once '../models/Product.php';
require_once '../models/Category.php';

// إنشاء كائنات النماذج
$storeModel = new Store($conn);
$productModel = new Product($conn);
$categoryModel = new Category($conn);

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// الحصول على متجر المستخدم
$store = $storeModel->getByUserId($userId);

// إذا لم يكن للمستخدم متجر، قم بتوجيهه لإنشاء متجر
if (!$store) {
    $_SESSION['info'] = 'يجب إنشاء متجر أولاً للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/vendor/create_store.php');
}

// الحصول على معرف المتجر
$storeId = $store['id'];

// التحقق من وجود جدول الفئات
$categoriesTableExists = false;
$result = $conn->query("SHOW TABLES LIKE 'categories'");
if ($result && $result->num_rows > 0) {
    $categoriesTableExists = true;
}

// الحصول على جميع الفئات
$categories = [];
if ($categoriesTableExists) {
    try {
        $categories = $categoryModel->getAll();
    } catch (Exception $e) {
        // تجاهل الخطأ واستخدام مصفوفة فارغة
    }
}

// معالجة إضافة منتج جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من وجود جدول الفئات
    if (!$categoriesTableExists) {
        $_SESSION['error'] = 'جدول الفئات غير موجود في قاعدة البيانات. يرجى تنفيذ ملف database/create_categories_table.sql لإنشاء الجدول.';
        redirect(APP_URL . '/vendor/add_product.php');
    }

    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/vendor/add_product.php');
    }

    // الحصول على البيانات المرسلة
    $name = sanitize($_POST['name']);
    $description = sanitize($_POST['description']);
    $price = floatval($_POST['price']);
    $stock_quantity = intval($_POST['stock_quantity']);
    $category_id = intval($_POST['category_id']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;

    // التحقق من البيانات
    $errors = [];

    if (empty($name)) {
        $errors[] = 'يرجى إدخال اسم المنتج';
    }

    if (empty($description)) {
        $errors[] = 'يرجى إدخال وصف المنتج';
    }

    if ($price <= 0) {
        $errors[] = 'يجب أن يكون السعر أكبر من صفر';
    }

    if ($stock_quantity < 0) {
        $errors[] = 'يجب أن تكون الكمية صفر أو أكبر';
    }

    // معالجة تحميل صورة المنتج
    $image = 'default_product.png'; // الصورة الافتراضية

    if (isset($_FILES['image']) && $_FILES['image']['error'] === 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2 ميجابايت

        if (!in_array($_FILES['image']['type'], $allowed_types)) {
            $errors[] = 'نوع الملف غير مسموح به. يرجى تحميل صورة بصيغة JPG أو PNG أو GIF';
        }

        if ($_FILES['image']['size'] > $max_size) {
            $errors[] = 'حجم الصورة كبير جداً. الحد الأقصى هو 2 ميجابايت';
        }

        if (empty($errors)) {
            // إنشاء اسم فريد للصورة
            $image = 'product_' . time() . '_' . $storeId . '.' . pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
            $upload_dir = PUBLIC_PATH . '/uploads/products/';

            // التأكد من وجود المجلد
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $upload_path = $upload_dir . $image;

            if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                $errors[] = 'حدث خطأ أثناء تحميل الصورة';
                $image = 'default_product.png'; // استخدام الصورة الافتراضية في حالة فشل التحميل
            }
        }
    }

    // إذا لم تكن هناك أخطاء، قم بإضافة المنتج
    if (empty($errors)) {
        $productModel->store_id = $storeId;
        $productModel->name = $name;
        $productModel->description = $description;
        $productModel->price = $price;
        $productModel->stock_quantity = $stock_quantity;
        $productModel->category_id = $category_id;
        $productModel->image = $image;
        $productModel->is_active = $is_active;
        $productModel->is_featured = $is_featured;

        if ($productModel->create()) {
            $_SESSION['success'] = 'تم إضافة المنتج بنجاح';
            redirect(APP_URL . '/vendor/products.php');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء إضافة المنتج';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// تضمين الرأس
include_once '../includes/vendor_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إضافة منتج جديد</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="<?php echo APP_URL; ?>/vendor/products.php" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة إلى المنتجات
                    </a>
                </div>
            </div>

            <!-- نموذج إضافة منتج -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <form action="" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($_POST['name']) ? $_POST['name'] : ''; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">وصف المنتج <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="description" name="description" rows="5" required><?php echo isset($_POST['description']) ? $_POST['description'] : ''; ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="price" class="form-label">السعر (ريال) <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="price" name="price" step="0.01" min="0.01" value="<?php echo isset($_POST['price']) ? $_POST['price'] : ''; ?>" required>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="stock_quantity" class="form-label">الكمية المتوفرة <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" min="0" value="<?php echo isset($_POST['stock_quantity']) ? $_POST['stock_quantity'] : '1'; ?>" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="category_id" class="form-label">الفئة <span class="text-danger">*</span></label>
                                    <?php if (!$categoriesTableExists): ?>
                                        <div class="alert alert-warning">
                                            <p class="mb-0">جدول الفئات غير موجود في قاعدة البيانات. يرجى تنفيذ ملف <code>database/create_categories_table.sql</code> لإنشاء الجدول.</p>
                                        </div>
                                        <select class="form-select" id="category_id" name="category_id" required disabled>
                                            <option value="">اختر الفئة</option>
                                        </select>
                                    <?php else: ?>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">اختر الفئة</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>" <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $category['name']; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="image" class="form-label">صورة المنتج</label>
                                    <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                    <div class="form-text">الحد الأقصى للحجم: 2 ميجابايت. الصيغ المسموح بها: JPG, PNG, GIF</div>
                                </div>

                                <div class="mb-3 text-center mt-4">
                                    <div class="position-relative d-inline-block">
                                        <?php
                                        // التحقق من وجود الصورة الافتراضية
                                        $defaultImagePath = PUBLIC_PATH . '/img/default_product.png';
                                        $defaultImageUrl = APP_URL . '/public/img/default_product.png';

                                        if (!file_exists($defaultImagePath)) {
                                            // إذا لم تكن الصورة موجودة، استخدم صورة افتراضية من الإنترنت
                                            $defaultImageUrl = 'https://via.placeholder.com/200x200?text=No+Image';
                                        }
                                        ?>
                                        <img src="<?php echo $defaultImageUrl; ?>" id="image-preview" class="img-thumbnail" style="width: 200px; height: 200px; object-fit: cover;">
                                    </div>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo (!isset($_POST['is_active']) || $_POST['is_active']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">منتج نشط (متاح للبيع)</label>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" <?php echo (isset($_POST['is_featured']) && $_POST['is_featured']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_featured">منتج مميز (يظهر في الصفحة الرئيسية)</label>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-outline-secondary">إعادة تعيين</button>
                            <button type="submit" class="btn btn-primary">إضافة المنتج</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معاينة الصورة قبل التحميل
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('image-preview');

    if (imageInput && imagePreview) {
        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    }
});
</script>

<?php
// تضمين التذييل
include_once '../includes/vendor_footer.php';
?>
