/* Main Styles for Zouk Market */

/* General Styles */
body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: #f8f9fa;
}

/* RTL Specific Adjustments */
.dropdown-menu-end {
    right: auto;
    left: 0;
}

/* Card Styles */
.card {
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-img-top {
    height: 180px;
    object-fit: cover;
}

/* Button Styles */
.btn {
    border-radius: 5px;
    padding: 0.5rem 1.5rem;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Footer */
footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

footer a {
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* Product Grid */
.product-grid .card-img-top {
    height: 150px;
}

/* Store Logo */
.store-logo {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Store Banner */
.store-banner {
    height: 200px;
    object-fit: cover;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Cart Badge */
.badge {
    position: relative;
    top: -8px;
}

/* Dashboard Sidebar */
.dashboard-sidebar {
    background-color: #343a40;
    color: #fff;
    min-height: calc(100vh - 56px);
}

.dashboard-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.8rem 1rem;
    border-radius: 5px;
    margin-bottom: 5px;
}

.dashboard-sidebar .nav-link:hover,
.dashboard-sidebar .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.dashboard-sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-right: 10px;
}

/* Order Status Badges */
.status-new {
    background-color: #0d6efd;
}

.status-processing {
    background-color: #ffc107;
    color: #000;
}

.status-ready {
    background-color: #6f42c1;
}

.status-delivering {
    background-color: #fd7e14;
}

.status-completed {
    background-color: #198754;
}

.status-cancelled {
    background-color: #dc3545;
}

/* Rating Stars */
.rating {
    color: #ffc107;
}

/* Product Details */
.product-details-img {
    max-height: 400px;
    object-fit: contain;
}

/* Quantity Input */
.quantity-input {
    width: 70px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-img-top {
        height: 140px;
    }

    .store-banner {
        height: 150px;
    }
}

/* Add Google Font - Tajawal (Arabic) */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* Rating Styles */
.stars {
    display: inline-flex;
    color: #ffc107;
}

.rating-bars .progress {
    border-radius: 5px;
    background-color: #f0f0f0;
}

/* Rating Input Styles */
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-input .stars {
    display: inline-flex;
    flex-direction: row-reverse;
}

.rating-input input[type="radio"] {
    display: none;
}

.rating-input label {
    cursor: pointer;
    font-size: 1.5rem;
    color: #ddd;
    margin: 0 2px;
}

.rating-input label:hover,
.rating-input label:hover ~ label,
.rating-input input[type="radio"]:checked ~ label {
    color: #ffc107;
}
