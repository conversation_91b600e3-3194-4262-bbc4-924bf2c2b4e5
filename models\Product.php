<?php
/**
 * Product Model
 *
 * Handles all product-related operations
 */
class Product {
    private $conn;
    private $table = 'products';

    // Product properties
    public $id;
    public $store_id;
    public $name;
    public $description;
    public $price;
    public $image;
    public $category_id;
    public $stock_quantity;
    public $is_active;
    public $is_featured;
    public $created_at;
    public $updated_at;

    /**
     * Constructor
     *
     * @param mysqli $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create a new product
     *
     * @return boolean
     */
    public function create() {
        // Create query
        $query = "INSERT INTO " . $this->table . "
                  SET store_id = ?,
                      name = ?,
                      description = ?,
                      price = ?,
                      image = ?,
                      category_id = ?,
                      stock_quantity = ?,
                      is_active = ?,
                      is_featured = ?,
                      created_at = NOW(),
                      updated_at = NOW()";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->store_id = htmlspecialchars(strip_tags($this->store_id));
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->price = htmlspecialchars(strip_tags($this->price));
        $this->image = htmlspecialchars(strip_tags($this->image));
        $this->category_id = htmlspecialchars(strip_tags($this->category_id));
        $this->stock_quantity = htmlspecialchars(strip_tags($this->stock_quantity));
        $this->is_active = $this->is_active ?? 1;
        $this->is_featured = $this->is_featured ?? 0;

        // Bind data
        $stmt->bind_param("issdsiiii",
            $this->store_id,
            $this->name,
            $this->description,
            $this->price,
            $this->image,
            $this->category_id,
            $this->stock_quantity,
            $this->is_active,
            $this->is_featured
        );

        // Execute query
        if($stmt->execute()) {
            $this->id = $this->conn->insert_id;
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Get product by ID
     *
     * @param int $id
     * @return array|boolean
     */
    public function getById($id) {
        // Create query
        $query = "SELECT p.*, c.name as category_name, s.name as store_name
                  FROM " . $this->table . " p
                  JOIN categories c ON p.category_id = c.id
                  JOIN stores s ON p.store_id = s.id
                  WHERE p.id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind ID
        $stmt->bind_param("i", $id);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        if($result->num_rows > 0) {
            return $result->fetch_assoc();
        } else {
            return false;
        }
    }

    /**
     * Get products by store ID
     *
     * @param int $storeId
     * @param int $categoryId Optional category filter
     * @param boolean $activeOnly Get only active products
     * @return array
     */
    public function getByStoreId($storeId, $categoryId = null, $activeOnly = true) {
        // Create query
        $query = "SELECT p.*, c.name as category_name
                  FROM " . $this->table . " p
                  JOIN categories c ON p.category_id = c.id
                  WHERE p.store_id = ?";

        // Add filters
        if($categoryId) {
            $query .= " AND p.category_id = ?";
        }

        if($activeOnly) {
            $query .= " AND p.is_active = 1";
        }

        $query .= " ORDER BY p.name";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind parameters
        if($categoryId) {
            $stmt->bind_param("ii", $storeId, $categoryId);
        } else {
            $stmt->bind_param("i", $storeId);
        }

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        $products = [];

        while($row = $result->fetch_assoc()) {
            $products[] = $row;
        }

        return $products;
    }

    /**
     * Get featured products
     *
     * @param int $limit Optional limit
     * @return array
     */
    public function getFeatured($limit = null) {
        // Create query
        $query = "SELECT p.*, c.name as category_name, s.name as store_name
                  FROM " . $this->table . " p
                  JOIN categories c ON p.category_id = c.id
                  JOIN stores s ON p.store_id = s.id
                  WHERE p.is_active = 1 AND p.is_featured = 1
                  ORDER BY p.created_at DESC";

        // Add limit if provided
        if($limit) {
            $query .= " LIMIT " . intval($limit);
        }

        // Execute query
        $result = $this->conn->query($query);

        $products = [];

        while($row = $result->fetch_assoc()) {
            $products[] = $row;
        }

        return $products;
    }

    /**
     * Update product
     *
     * @return boolean
     */
    public function update() {
        // الحصول على بيانات المنتج الحالي
        $currentProduct = $this->getById($this->id);
        if (!$currentProduct) {
            return false;
        }

        // إنشاء الاستعلام
        $query = "UPDATE " . $this->table . " SET ";
        $params = [];
        $types = "";
        $fields = [];

        // إضافة الحقول فقط إذا تم تعيينها
        if (isset($this->name)) {
            $fields[] = "name = ?";
            $params[] = htmlspecialchars(strip_tags($this->name));
            $types .= "s";
        }

        if (isset($this->description)) {
            $fields[] = "description = ?";
            $params[] = htmlspecialchars(strip_tags($this->description));
            $types .= "s";
        }

        if (isset($this->price)) {
            $fields[] = "price = ?";
            $params[] = htmlspecialchars(strip_tags($this->price));
            $types .= "d";
        }

        if (isset($this->image) && $this->image) {
            $fields[] = "image = ?";
            $params[] = htmlspecialchars(strip_tags($this->image));
            $types .= "s";
        }

        if (isset($this->category_id)) {
            $fields[] = "category_id = ?";
            $params[] = htmlspecialchars(strip_tags($this->category_id));
            $types .= "i";
        }

        if (isset($this->stock_quantity)) {
            $fields[] = "stock_quantity = ?";
            $params[] = htmlspecialchars(strip_tags($this->stock_quantity));
            $types .= "i";
        }

        if (isset($this->is_active)) {
            $fields[] = "is_active = ?";
            $params[] = htmlspecialchars(strip_tags($this->is_active));
            $types .= "i";
        }

        if (isset($this->is_featured)) {
            $fields[] = "is_featured = ?";
            $params[] = htmlspecialchars(strip_tags($this->is_featured));
            $types .= "i";
        }

        // إضافة حقل updated_at
        $fields[] = "updated_at = NOW()";

        // إذا لم تكن هناك حقول للتحديث، قم بإرجاع true
        if (empty($fields)) {
            return true;
        }

        // إكمال الاستعلام
        $query .= implode(", ", $fields) . " WHERE id = ?";

        // إضافة معلمة ID
        $params[] = htmlspecialchars(strip_tags($this->id));
        $types .= "i";

        // إعداد الاستعلام
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bind_param($types, ...$params);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Delete product
     *
     * @param int $id
     * @param int|null $storeId Optional store ID for security check
     * @return boolean
     */
    public function delete($id, $storeId = null) {
        // Create query
        if ($storeId !== null) {
            // If store ID is provided, ensure the product belongs to the store
            $query = "DELETE FROM " . $this->table . " WHERE id = ? AND store_id = ?";

            // Prepare statement
            $stmt = $this->conn->prepare($query);

            // Bind data
            $stmt->bind_param("ii", $id, $storeId);
        } else {
            // If no store ID is provided, just delete by ID
            $query = "DELETE FROM " . $this->table . " WHERE id = ?";

            // Prepare statement
            $stmt = $this->conn->prepare($query);

            // Bind data
            $stmt->bind_param("i", $id);
        }

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Search products
     *
     * @param string $keyword
     * @param int $storeId Optional store filter
     * @return array
     */
    public function search($keyword, $storeId = null) {
        // Create query
        $query = "SELECT p.*, c.name as category_name, s.name as store_name
                  FROM " . $this->table . " p
                  JOIN categories c ON p.category_id = c.id
                  JOIN stores s ON p.store_id = s.id
                  WHERE p.is_active = 1
                  AND (p.name LIKE ? OR p.description LIKE ?)";

        // Add store filter if provided
        if($storeId) {
            $query .= " AND p.store_id = ?";
        }

        $query .= " ORDER BY p.name";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Prepare search term
        $searchTerm = "%" . $keyword . "%";

        // Bind data
        if($storeId) {
            $stmt->bind_param("ssi", $searchTerm, $searchTerm, $storeId);
        } else {
            $stmt->bind_param("ss", $searchTerm, $searchTerm);
        }

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        $products = [];

        while($row = $result->fetch_assoc()) {
            $products[] = $row;
        }

        return $products;
    }

    /**
     * Update product stock
     *
     * @param int $id
     * @param int $quantity
     * @return boolean
     */
    public function updateStock($id, $quantity) {
        // Create query
        $query = "UPDATE " . $this->table . "
                  SET stock_quantity = stock_quantity - ?,
                      updated_at = NOW()
                  WHERE id = ? AND stock_quantity >= ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bind_param("iii", $quantity, $id, $quantity);

        // Execute query
        if($stmt->execute()) {
            return $stmt->affected_rows > 0;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }
}
