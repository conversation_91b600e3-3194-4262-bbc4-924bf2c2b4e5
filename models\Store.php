<?php
/**
 * Store Model
 *
 * Handles all store-related operations
 */
class Store {
    private $conn;
    private $table = 'stores';

    // Store properties
    public $id;
    public $user_id;
    public $name;
    public $description;
    public $logo;
    public $banner;
    public $address;
    public $phone;
    public $category_id;
    public $is_active;
    public $is_featured;
    public $created_at;
    public $updated_at;

    /**
     * Constructor
     *
     * @param mysqli $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create a new store
     *
     * @return boolean
     */
    public function create() {
        // Create query
        $query = "INSERT INTO " . $this->table . "
                  SET user_id = ?,
                      name = ?,
                      description = ?,
                      logo = ?,
                      banner = ?,
                      address = ?,
                      phone = ?,
                      category_id = ?,
                      is_active = ?,
                      is_featured = ?,
                      created_at = NOW(),
                      updated_at = NOW()";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->user_id = htmlspecialchars(strip_tags($this->user_id));
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->logo = htmlspecialchars(strip_tags($this->logo));
        $this->banner = htmlspecialchars(strip_tags($this->banner));
        $this->address = htmlspecialchars(strip_tags($this->address));
        $this->phone = htmlspecialchars(strip_tags($this->phone));
        $this->category_id = htmlspecialchars(strip_tags($this->category_id));
        $this->is_active = $this->is_active ?? 0; // Default to inactive (pending approval)
        $this->is_featured = $this->is_featured ?? 0;

        // Bind data
        $stmt->bind_param("issssssiis",
            $this->user_id,
            $this->name,
            $this->description,
            $this->logo,
            $this->banner,
            $this->address,
            $this->phone,
            $this->category_id,
            $this->is_active,
            $this->is_featured
        );

        // Execute query
        if($stmt->execute()) {
            $this->id = $this->conn->insert_id;
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Get store by ID
     *
     * @param int $id
     * @return array|boolean
     */
    public function getById($id) {
        // Create query
        $query = "SELECT * FROM " . $this->table . " WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind ID
        $stmt->bind_param("i", $id);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        if($result->num_rows > 0) {
            return $result->fetch_assoc();
        } else {
            return false;
        }
    }

    /**
     * Get store by user ID
     *
     * @param int $userId
     * @return array|boolean
     */
    public function getByUserId($userId) {
        // Create query
        $query = "SELECT * FROM " . $this->table . " WHERE user_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind user ID
        $stmt->bind_param("i", $userId);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        if($result->num_rows > 0) {
            return $result->fetch_assoc();
        } else {
            return false;
        }
    }

    /**
     * Get all stores
     *
     * @param int $categoryId Optional category filter
     * @param boolean $activeOnly Get only active stores
     * @param boolean $featuredOnly Get only featured stores
     * @param int $limit Optional limit
     * @return array
     */
    public function getAll($categoryId = null, $activeOnly = true, $featuredOnly = false, $limit = null) {
        // Create query
        $query = "SELECT s.*, sc.name as category_name
                  FROM " . $this->table . " s
                  LEFT JOIN store_categories sc ON s.category_id = sc.id
                  WHERE 1=1";

        // Add filters
        if($categoryId) {
            $query .= " AND s.category_id = " . intval($categoryId);
        }

        if($activeOnly) {
            $query .= " AND s.is_active = 1";
        }

        if($featuredOnly) {
            $query .= " AND s.is_featured = 1";
        }

        $query .= " ORDER BY s.name";

        // Add limit if provided
        if($limit) {
            $query .= " LIMIT " . intval($limit);
        }

        // Execute query
        $result = $this->conn->query($query);

        $stores = [];

        while($row = $result->fetch_assoc()) {
            $stores[] = $row;
        }

        return $stores;
    }

    /**
     * Update store
     *
     * @return boolean
     */
    public function update() {
        // Get current store data
        $currentStore = $this->getById($this->id);
        if (!$currentStore) {
            return false;
        }

        // Create query
        $query = "UPDATE " . $this->table . " SET ";
        $params = [];
        $types = "";
        $fields = [];

        // Add fields only if they are set
        if (isset($this->name)) {
            $fields[] = "name = ?";
            $params[] = htmlspecialchars(strip_tags($this->name));
            $types .= "s";
        }

        if (isset($this->description)) {
            $fields[] = "description = ?";
            $params[] = htmlspecialchars(strip_tags($this->description));
            $types .= "s";
        }

        if (isset($this->logo) && $this->logo) {
            $fields[] = "logo = ?";
            $params[] = htmlspecialchars(strip_tags($this->logo));
            $types .= "s";
        }

        if (isset($this->banner) && $this->banner) {
            $fields[] = "banner = ?";
            $params[] = htmlspecialchars(strip_tags($this->banner));
            $types .= "s";
        }

        if (isset($this->address)) {
            $fields[] = "address = ?";
            $params[] = htmlspecialchars(strip_tags($this->address));
            $types .= "s";
        }

        if (isset($this->phone)) {
            $fields[] = "phone = ?";
            $params[] = htmlspecialchars(strip_tags($this->phone));
            $types .= "s";
        }

        if (isset($this->category_id)) {
            $fields[] = "category_id = ?";
            $params[] = htmlspecialchars(strip_tags($this->category_id));
            $types .= "i";
        }

        if (isset($this->is_active)) {
            $fields[] = "is_active = ?";
            $params[] = htmlspecialchars(strip_tags($this->is_active));
            $types .= "i";
        }

        if (isset($this->is_featured)) {
            $fields[] = "is_featured = ?";
            $params[] = htmlspecialchars(strip_tags($this->is_featured));
            $types .= "i";
        }

        // Add updated_at field
        $fields[] = "updated_at = NOW()";

        // If no fields to update, return true
        if (empty($fields)) {
            return true;
        }

        // Complete the query
        $query .= implode(", ", $fields) . " WHERE id = ?";

        // Add ID parameter
        $params[] = htmlspecialchars(strip_tags($this->id));
        $types .= "i";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bind_param($types, ...$params);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Delete store
     *
     * @param int $id
     * @return boolean
     */
    public function delete($id) {
        // Create query
        $query = "DELETE FROM " . $this->table . " WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bind_param("i", $id);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Get pending stores
     *
     * @return array
     */
    public function getPendingStores() {
        // Create query
        $query = "SELECT s.*, sc.name as category_name, u.name as owner_name, u.email as owner_email
                  FROM " . $this->table . " s
                  JOIN store_categories sc ON s.category_id = sc.id
                  JOIN users u ON s.user_id = u.id
                  WHERE s.is_active = 0
                  ORDER BY s.created_at DESC";

        // Execute query
        $result = $this->conn->query($query);

        $stores = [];

        while($row = $result->fetch_assoc()) {
            $stores[] = $row;
        }

        return $stores;
    }

    /**
     * Approve store
     *
     * @param int $id
     * @return boolean
     */
    public function approve($id) {
        // Create query
        $query = "UPDATE " . $this->table . "
                  SET is_active = 1,
                      updated_at = NOW()
                  WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bind_param("i", $id);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Search stores
     *
     * @param string $keyword
     * @return array
     */
    public function search($keyword) {
        // Create query
        $query = "SELECT s.*, sc.name as category_name
                  FROM " . $this->table . " s
                  JOIN store_categories sc ON s.category_id = sc.id
                  WHERE s.is_active = 1
                  AND (s.name LIKE ? OR s.description LIKE ?)
                  ORDER BY s.name";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Prepare search term
        $searchTerm = "%" . $keyword . "%";

        // Bind data
        $stmt->bind_param("ss", $searchTerm, $searchTerm);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        $stores = [];

        while($row = $result->fetch_assoc()) {
            $stores[] = $row;
        }

        return $stores;
    }
}
