<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المتجر
require_once '../models/Store.php';

// إنشاء كائن المتجر
$storeModel = new Store($conn);

// معالجة الإجراءات
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $storeId = isset($_GET['id']) ? intval($_GET['id']) : 0;

    // التحقق من وجود المتجر
    $store = $storeModel->getById($storeId);

    if (!$store) {
        $_SESSION['error'] = 'المتجر غير موجود';
        redirect(APP_URL . '/admin/stores.php');
    }

    // تنفيذ الإجراء المطلوب
    switch ($action) {
        case 'activate':
            // تفعيل المتجر
            if ($storeModel->approve($storeId)) {
                $_SESSION['success'] = 'تم تفعيل المتجر بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء تفعيل المتجر';
            }
            break;

        case 'deactivate':
            // تعطيل المتجر
            $storeModel = new Store($conn);
            $storeModel->id = $storeId;
            $storeModel->is_active = 0;
            if ($storeModel->update()) {
                $_SESSION['success'] = 'تم تعطيل المتجر بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء تعطيل المتجر';
            }
            break;

        case 'feature':
            // تمييز المتجر
            $storeModel = new Store($conn);
            $storeModel->id = $storeId;
            $storeModel->is_featured = 1;
            if ($storeModel->update()) {
                $_SESSION['success'] = 'تم تمييز المتجر بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء تمييز المتجر';
            }
            break;

        case 'unfeature':
            // إلغاء تمييز المتجر
            $storeModel = new Store($conn);
            $storeModel->id = $storeId;
            $storeModel->is_featured = 0;
            if ($storeModel->update()) {
                $_SESSION['success'] = 'تم إلغاء تمييز المتجر بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء إلغاء تمييز المتجر';
            }
            break;

        case 'delete':
            // حذف المتجر
            if ($storeModel->delete($storeId)) {
                $_SESSION['success'] = 'تم حذف المتجر بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف المتجر';
            }
            break;
    }

    redirect(APP_URL . '/admin/stores.php');
}

// الحصول على قائمة المتاجر
$categoryId = isset($_GET['category']) ? intval($_GET['category']) : null;
$activeOnly = isset($_GET['active']) ? ($_GET['active'] == '1') : false;
$featuredOnly = isset($_GET['featured']) ? ($_GET['featured'] == '1') : false;
$stores = $storeModel->getAll($categoryId, $activeOnly, $featuredOnly);

// الحصول على قائمة الفئات
$sql = "SELECT * FROM store_categories ORDER BY name";
$result = $conn->query($sql);
$categories = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
}

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المتاجر</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="<?php echo APP_URL; ?>/admin/pending_stores.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-clipboard-list"></i> طلبات المتاجر الجديدة
                        </a>
                        <a href="<?php echo APP_URL; ?>/admin/categories.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-tags"></i> إدارة الفئات
                        </a>
                    </div>
                </div>
            </div>

            <!-- فلتر المتاجر -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">فلتر المتاجر</h5>
                </div>
                <div class="card-body">
                    <form action="" method="GET">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="category" class="form-label">الفئة</label>
                                <select name="category" id="category" class="form-select">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" <?php echo $categoryId == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo $category['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="active" class="form-label">الحالة</label>
                                <select name="active" id="active" class="form-select">
                                    <option value="">الكل</option>
                                    <option value="1" <?php echo $activeOnly ? 'selected' : ''; ?>>المتاجر النشطة فقط</option>
                                    <option value="0" <?php echo $activeOnly === false && isset($_GET['active']) ? 'selected' : ''; ?>>المتاجر غير النشطة فقط</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="featured" class="form-label">التمييز</label>
                                <select name="featured" id="featured" class="form-select">
                                    <option value="">الكل</option>
                                    <option value="1" <?php echo $featuredOnly ? 'selected' : ''; ?>>المتاجر المميزة فقط</option>
                                    <option value="0" <?php echo $featuredOnly === false && isset($_GET['featured']) ? 'selected' : ''; ?>>المتاجر غير المميزة فقط</option>
                                </select>
                            </div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">تصفية</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة المتاجر -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">قائمة المتاجر</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الشعار</th>
                                    <th>اسم المتجر</th>
                                    <th>المالك</th>
                                    <th>الفئة</th>
                                    <th>الحالة</th>
                                    <th>مميز</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($stores)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center">لا يوجد متاجر</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($stores as $store): ?>
                                        <tr>
                                            <td><?php echo $store['id']; ?></td>
                                            <td>
                                                <img src="<?php echo APP_URL; ?>/public/uploads/stores/<?php echo $store['logo']; ?>" alt="<?php echo $store['name']; ?>" class="img-thumbnail" width="50">
                                            </td>
                                            <td><?php echo $store['name']; ?></td>
                                            <td>
                                                <?php
                                                $sql = "SELECT name, email FROM users WHERE id = " . $store['user_id'];
                                                $result = $conn->query($sql);
                                                $owner = $result->fetch_assoc();
                                                echo $owner['name'] . '<br><small class="text-muted">' . $owner['email'] . '</small>';
                                                ?>
                                            </td>
                                            <td><?php echo isset($store['category_name']) ? $store['category_name'] : 'غير مصنف'; ?></td>
                                            <td>
                                                <?php if ($store['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($store['is_featured']): ?>
                                                    <span class="badge bg-warning text-dark">مميز</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير مميز</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($store['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo APP_URL; ?>/admin/store_details.php?id=<?php echo $store['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo APP_URL; ?>/admin/edit_store.php?id=<?php echo $store['id']; ?>" class="btn btn-sm btn-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($store['is_active']): ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/stores.php?action=deactivate&id=<?php echo $store['id']; ?>" class="btn btn-sm btn-warning" title="تعطيل" onclick="return confirm('هل أنت متأكد من تعطيل هذا المتجر؟')">
                                                            <i class="fas fa-ban"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/stores.php?action=activate&id=<?php echo $store['id']; ?>" class="btn btn-sm btn-success" title="تفعيل" onclick="return confirm('هل أنت متأكد من تفعيل هذا المتجر؟')">
                                                            <i class="fas fa-check"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($store['is_featured']): ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/stores.php?action=unfeature&id=<?php echo $store['id']; ?>" class="btn btn-sm btn-secondary" title="إلغاء التمييز" onclick="return confirm('هل أنت متأكد من إلغاء تمييز هذا المتجر؟')">
                                                            <i class="fas fa-star-half-alt"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/stores.php?action=feature&id=<?php echo $store['id']; ?>" class="btn btn-sm btn-warning text-dark" title="تمييز" onclick="return confirm('هل أنت متأكد من تمييز هذا المتجر؟')">
                                                            <i class="fas fa-star"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <a href="<?php echo APP_URL; ?>/admin/stores.php?action=delete&id=<?php echo $store['id']; ?>" class="btn btn-sm btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا المتجر؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
