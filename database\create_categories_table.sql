-- إنشاء جدول الفئات
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إضافة بعض الفئات الافتراضية
INSERT INTO `categories` (`name`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
('مطاعم', 'جميع أنواع المطاعم', 1, NOW(), NOW()),
('سوبرماركت', 'محلات البقالة والسوبرماركت', 1, NOW(), NOW()),
('مخابز', 'المخابز ومحلات الحلويات', 1, NOW(), NOW()),
('مقاهي', 'المقاهي ومحلات القهوة', 1, NOW(), NOW()),
('فواكه وخضروات', 'محلات الفواكه والخضروات الطازجة', 1, NOW(), NOW()),
('لحوم وأسماك', 'محلات اللحوم والأسماك الطازجة', 1, NOW(), NOW()),
('وجبات سريعة', 'مطاعم الوجبات السريعة', 1, NOW(), NOW()),
('مأكولات شرقية', 'مطاعم المأكولات الشرقية', 1, NOW(), NOW()),
('مأكولات غربية', 'مطاعم المأكولات الغربية', 1, NOW(), NOW()),
('مشروبات', 'محلات المشروبات والعصائر', 1, NOW(), NOW());
