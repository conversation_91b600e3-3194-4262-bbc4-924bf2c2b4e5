<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المنتج
require_once '../models/Product.php';
require_once '../models/Store.php';

// التحقق من وجود معرف المنتج
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف المنتج غير صالح';
    redirect(APP_URL . '/admin/products.php');
}

$productId = intval($_GET['id']);

// إنشاء كائن المنتج
$productModel = new Product($conn);
$storeModel = new Store($conn);

// الحصول على بيانات المنتج
$product = $productModel->getById($productId);

if (!$product) {
    $_SESSION['error'] = 'المنتج غير موجود';
    redirect(APP_URL . '/admin/products.php');
}

// الحصول على بيانات المتجر
$store = $storeModel->getById($product['store_id']);

// الحصول على إحصائيات المنتج
$sql = "SELECT COUNT(*) as order_count, SUM(oi.quantity) as total_sold 
        FROM order_items oi 
        JOIN orders o ON oi.order_id = o.id 
        WHERE oi.product_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $productId);
$stmt->execute();
$stats = $stmt->get_result()->fetch_assoc();

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تفاصيل المنتج</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="<?php echo APP_URL; ?>/admin/products.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-right"></i> العودة إلى قائمة المنتجات
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- معلومات المنتج -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">صورة المنتج</h5>
                        </div>
                        <div class="card-body text-center">
                            <img src="<?php echo APP_URL; ?>/public/uploads/products/<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" class="img-fluid rounded mb-3" style="max-height: 300px;">
                            
                            <div class="d-grid gap-2 mt-3">
                                <?php if ($product['is_active']): ?>
                                    <a href="<?php echo APP_URL; ?>/admin/products.php?action=deactivate&id=<?php echo $productId; ?>" class="btn btn-warning" onclick="return confirm('هل أنت متأكد من تعطيل هذا المنتج؟')">
                                        <i class="fas fa-ban"></i> تعطيل المنتج
                                    </a>
                                <?php else: ?>
                                    <a href="<?php echo APP_URL; ?>/admin/products.php?action=activate&id=<?php echo $productId; ?>" class="btn btn-success" onclick="return confirm('هل أنت متأكد من تفعيل هذا المنتج؟')">
                                        <i class="fas fa-check"></i> تفعيل المنتج
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($product['is_featured']): ?>
                                    <a href="<?php echo APP_URL; ?>/admin/products.php?action=unfeature&id=<?php echo $productId; ?>" class="btn btn-secondary" onclick="return confirm('هل أنت متأكد من إلغاء تمييز هذا المنتج؟')">
                                        <i class="fas fa-star-half-alt"></i> إلغاء التمييز
                                    </a>
                                <?php else: ?>
                                    <a href="<?php echo APP_URL; ?>/admin/products.php?action=feature&id=<?php echo $productId; ?>" class="btn btn-warning" onclick="return confirm('هل أنت متأكد من تمييز هذا المنتج؟')">
                                        <i class="fas fa-star"></i> تمييز المنتج
                                    </a>
                                <?php endif; ?>
                                
                                <a href="<?php echo APP_URL; ?>/admin/products.php?action=delete&id=<?php echo $productId; ?>" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                    <i class="fas fa-trash"></i> حذف المنتج
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">معلومات المنتج</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">اسم المنتج</label>
                                        <p class="form-control"><?php echo $product['name']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">المتجر</label>
                                        <p class="form-control">
                                            <a href="<?php echo APP_URL; ?>/admin/store_details.php?id=<?php echo $store['id']; ?>">
                                                <?php echo $store['name']; ?>
                                            </a>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الفئة</label>
                                        <p class="form-control"><?php echo $product['category_name']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">السعر</label>
                                        <p class="form-control"><?php echo number_format($product['price'], 2); ?> ريال</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الكمية المتوفرة</label>
                                        <p class="form-control"><?php echo $product['stock_quantity']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الحالة</label>
                                        <p class="form-control">
                                            <?php if ($product['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">مميز</label>
                                        <p class="form-control">
                                            <?php if ($product['is_featured']): ?>
                                                <span class="badge bg-warning text-dark">نعم</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">لا</span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">تاريخ الإضافة</label>
                                        <p class="form-control"><?php echo date('Y-m-d', strtotime($product['created_at'])); ?></p>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">الوصف</label>
                                        <div class="form-control" style="min-height: 100px;"><?php echo $product['description']; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إحصائيات المنتج -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">إحصائيات المنتج</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">عدد الطلبات</h5>
                                            <h2 class="mb-0"><?php echo $stats['order_count'] ?? 0; ?></h2>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">إجمالي المبيعات</h5>
                                            <h2 class="mb-0"><?php echo $stats['total_sold'] ?? 0; ?> وحدة</h2>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- آخر الطلبات التي تتضمن هذا المنتج -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">آخر الطلبات التي تتضمن هذا المنتج</h5>
                </div>
                <div class="card-body">
                    <?php
                    $sql = "SELECT o.*, oi.quantity, oi.price, u.name as customer_name 
                            FROM orders o
                            JOIN order_items oi ON o.id = oi.order_id
                            JOIN users u ON o.user_id = u.id
                            WHERE oi.product_id = ?
                            ORDER BY o.created_at DESC
                            LIMIT 10";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("i", $productId);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $orders = [];
                    
                    if ($result && $result->num_rows > 0) {
                        while ($row = $result->fetch_assoc()) {
                            $orders[] = $row;
                        }
                    }
                    ?>
                    
                    <?php if (empty($orders)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد طلبات لهذا المنتج حتى الآن.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                        <th>حالة الطلب</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orders as $order): ?>
                                        <tr>
                                            <td><?php echo $order['id']; ?></td>
                                            <td><?php echo $order['customer_name']; ?></td>
                                            <td><?php echo $order['quantity']; ?></td>
                                            <td><?php echo number_format($order['price'], 2); ?> ريال</td>
                                            <td><?php echo number_format($order['quantity'] * $order['price'], 2); ?> ريال</td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                switch ($order['status']) {
                                                    case 'new': $statusClass = 'bg-primary'; break;
                                                    case 'processing': $statusClass = 'bg-warning text-dark'; break;
                                                    case 'ready': $statusClass = 'bg-info'; break;
                                                    case 'delivering': $statusClass = 'bg-info'; break;
                                                    case 'completed': $statusClass = 'bg-success'; break;
                                                    case 'cancelled': $statusClass = 'bg-danger'; break;
                                                }
                                                echo '<span class="badge ' . $statusClass . '">' . $order['status'] . '</span>';
                                                ?>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></td>
                                            <td>
                                                <a href="<?php echo APP_URL; ?>/admin/order_details.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
