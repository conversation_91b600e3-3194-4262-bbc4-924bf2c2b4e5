<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول ودور المستخدم
if (!isLoggedIn() || !isVendor()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كبائع للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// تضمين النماذج المطلوبة
require_once '../models/Store.php';
require_once '../models/Order.php';

// إنشاء كائنات النماذج
$storeModel = new Store($conn);
$orderModel = new Order($conn);

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// الحصول على متجر المستخدم
$store = $storeModel->getByUserId($userId);

// إذا لم يكن للمستخدم متجر، قم بتوجيهه لإنشاء متجر
if (!$store) {
    $_SESSION['info'] = 'يجب إنشاء متجر أولاً للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/vendor/create_store.php');
}

// الحصول على معرف المتجر
$storeId = $store['id'];

// التحقق من وجود معرف الطلب
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'معرف الطلب غير صحيح';
    redirect(APP_URL . '/vendor/orders.php');
}

$orderId = intval($_GET['id']);

// الحصول على بيانات الطلب
$order = $orderModel->getById($orderId);

// التحقق من أن الطلب ينتمي إلى المتجر
if (!$order || $order['store_id'] != $storeId) {
    $_SESSION['error'] = 'لا يمكنك تحديث حالة هذا الطلب';
    redirect(APP_URL . '/vendor/orders.php');
}

// التحقق من أن الطلب ليس مكتملاً أو ملغياً
if ($order['status'] == 'completed' || $order['status'] == 'cancelled') {
    $_SESSION['error'] = 'لا يمكن تحديث حالة الطلب المكتمل أو الملغي';
    redirect(APP_URL . '/vendor/order_details.php?id=' . $orderId);
}

// معالجة تحديث حالة الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/vendor/update_order_status.php?id=' . $orderId);
    }
    
    // الحصول على البيانات المرسلة
    $newStatus = sanitize($_POST['status']);
    $notes = sanitize($_POST['notes']);
    
    // التحقق من البيانات
    $errors = [];
    
    if (empty($newStatus)) {
        $errors[] = 'يرجى اختيار حالة الطلب';
    }
    
    // التحقق من أن الحالة الجديدة مختلفة عن الحالة الحالية
    if ($newStatus == $order['status']) {
        $errors[] = 'الحالة الجديدة هي نفس الحالة الحالية';
    }
    
    // التحقق من أن الحالة الجديدة صالحة
    $validStatuses = ['new', 'processing', 'ready', 'delivering', 'completed', 'cancelled'];
    if (!in_array($newStatus, $validStatuses)) {
        $errors[] = 'حالة الطلب غير صالحة';
    }
    
    // إذا لم تكن هناك أخطاء، قم بتحديث حالة الطلب
    if (empty($errors)) {
        // تحديث حالة الطلب
        if ($orderModel->updateStatus($orderId, $newStatus, $storeId)) {
            // إضافة سجل في تاريخ الطلب
            $sql = "INSERT INTO order_history (order_id, user_id, old_status, new_status, notes, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("iisss", $orderId, $userId, $order['status'], $newStatus, $notes);
            $stmt->execute();
            
            // إضافة إشعار للعميل
            $notificationTitle = 'تحديث حالة الطلب #' . $orderId;
            $notificationMessage = 'تم تحديث حالة طلبك #' . $orderId . ' من ' . getStatusText($order['status']) . ' إلى ' . getStatusText($newStatus);
            
            // التحقق من وجود جدول الإشعارات
            $tableExists = false;
            $result = $conn->query("SHOW TABLES LIKE 'notifications'");
            if ($result && $result->num_rows > 0) {
                $tableExists = true;
            }
            
            if ($tableExists) {
                $sql = "INSERT INTO notifications (user_id, title, message, is_read, created_at) 
                        VALUES (?, ?, ?, 0, NOW())";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("iss", $order['user_id'], $notificationTitle, $notificationMessage);
                $stmt->execute();
            }
            
            $_SESSION['success'] = 'تم تحديث حالة الطلب بنجاح';
            redirect(APP_URL . '/vendor/order_details.php?id=' . $orderId);
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة الطلب';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// دالة للحصول على النص العربي لحالة الطلب
function getStatusText($status) {
    switch ($status) {
        case 'new':
            return 'جديد';
        case 'processing':
            return 'قيد التجهيز';
        case 'ready':
            return 'جاهز للتسليم';
        case 'delivering':
            return 'قيد التوصيل';
        case 'completed':
            return 'مكتمل';
        case 'cancelled':
            return 'ملغي';
        default:
            return $status;
    }
}

// تضمين الرأس
include_once '../includes/vendor_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تحديث حالة الطلب #<?php echo $order['id']; ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="<?php echo APP_URL; ?>/vendor/order_details.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة إلى تفاصيل الطلب
                    </a>
                </div>
            </div>
            
            <!-- معلومات الطلب الحالية -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات الطلب الحالية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">رقم الطلب:</th>
                                    <td>#<?php echo $order['id']; ?></td>
                                </tr>
                                <tr>
                                    <th>تاريخ الطلب:</th>
                                    <td><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></td>
                                </tr>
                                <tr>
                                    <th>العميل:</th>
                                    <td><?php echo $order['user_name']; ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">الحالة الحالية:</th>
                                    <td>
                                        <?php
                                        $statusClass = '';
                                        $statusText = '';
                                        switch ($order['status']) {
                                            case 'new':
                                                $statusClass = 'bg-primary';
                                                $statusText = 'جديد';
                                                break;
                                            case 'processing':
                                                $statusClass = 'bg-warning text-dark';
                                                $statusText = 'قيد التجهيز';
                                                break;
                                            case 'ready':
                                                $statusClass = 'bg-info';
                                                $statusText = 'جاهز للتسليم';
                                                break;
                                            case 'delivering':
                                                $statusClass = 'bg-info';
                                                $statusText = 'قيد التوصيل';
                                                break;
                                            case 'completed':
                                                $statusClass = 'bg-success';
                                                $statusText = 'مكتمل';
                                                break;
                                            case 'cancelled':
                                                $statusClass = 'bg-danger';
                                                $statusText = 'ملغي';
                                                break;
                                        }
                                        echo '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>طريقة الدفع:</th>
                                    <td>
                                        <?php
                                        switch ($order['payment_method']) {
                                            case 'cash':
                                                echo 'نقداً عند الاستلام';
                                                break;
                                            case 'card':
                                                echo 'بطاقة ائتمان';
                                                break;
                                            case 'bank_transfer':
                                                echo 'تحويل بنكي';
                                                break;
                                            default:
                                                echo $order['payment_method'];
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>حالة الدفع:</th>
                                    <td>
                                        <?php if ($order['payment_status'] == 'paid'): ?>
                                            <span class="badge bg-success">مدفوع</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning text-dark">غير مدفوع</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- نموذج تحديث الحالة -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">تحديث حالة الطلب</h5>
                </div>
                <div class="card-body">
                    <form action="" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة الجديدة <span class="text-danger">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="">اختر الحالة الجديدة</option>
                                <?php if ($order['status'] == 'new'): ?>
                                    <option value="processing">قيد التجهيز</option>
                                    <option value="cancelled">ملغي</option>
                                <?php elseif ($order['status'] == 'processing'): ?>
                                    <option value="ready">جاهز للتسليم</option>
                                    <option value="cancelled">ملغي</option>
                                <?php elseif ($order['status'] == 'ready'): ?>
                                    <option value="delivering">قيد التوصيل</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="cancelled">ملغي</option>
                                <?php elseif ($order['status'] == 'delivering'): ?>
                                    <option value="completed">مكتمل</option>
                                    <option value="cancelled">ملغي</option>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="أضف ملاحظات حول سبب تغيير الحالة (اختياري)"><?php echo isset($_POST['notes']) ? $_POST['notes'] : ''; ?></textarea>
                        </div>
                        
                        <div class="alert alert-info">
                            <p class="mb-0"><i class="fas fa-info-circle"></i> سيتم إرسال إشعار للعميل عند تحديث حالة الطلب.</p>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?php echo APP_URL; ?>/vendor/order_details.php?id=<?php echo $order['id']; ?>" class="btn btn-outline-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">تحديث الحالة</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/vendor_footer.php';
?>
