<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Verificar si el usuario está logueado como administrador
if(!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// Ejecutar scripts SQL
$scripts = [
    'create_reviews_table.sql',
    'create_store_reviews_table.sql',
    'update_reviews_table.sql'
];

$success = true;
$messages = [];

foreach($scripts as $script) {
    $scriptPath = ROOT_PATH . '/database/' . $script;
    
    if(file_exists($scriptPath)) {
        $sql = file_get_contents($scriptPath);
        
        if($conn->multi_query($sql)) {
            $messages[] = 'تم تنفيذ الملف ' . $script . ' بنجاح';
            
            // Limpiar resultados pendientes
            while($conn->more_results() && $conn->next_result()) {
                if($result = $conn->store_result()) {
                    $result->free();
                }
            }
        } else {
            $success = false;
            $messages[] = 'حدث خطأ أثناء تنفيذ الملف ' . $script . ': ' . $conn->error;
        }
    } else {
        $messages[] = 'الملف ' . $script . ' غير موجود';
    }
}

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">تحديث قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <?php if($success): ?>
                        <div class="alert alert-success">
                            <p class="mb-0">تم تحديث قاعدة البيانات بنجاح</p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <p class="mb-0">حدث خطأ أثناء تحديث قاعدة البيانات</p>
                        </div>
                    <?php endif; ?>
                    
                    <h6>رسائل التنفيذ:</h6>
                    <ul>
                        <?php foreach($messages as $message): ?>
                            <li><?php echo $message; ?></li>
                        <?php endforeach; ?>
                    </ul>
                    
                    <div class="mt-3">
                        <a href="<?php echo APP_URL; ?>/admin/dashboard.php" class="btn btn-primary">العودة إلى لوحة التحكم</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
