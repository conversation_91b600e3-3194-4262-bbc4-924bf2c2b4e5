<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول للوصول إلى الملف الشخصي';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المستخدم
require_once '../models/User.php';
require_once '../models/Order.php';

// إنشاء كائن المستخدم والطلب
$userModel = new User($conn);
$orderModel = new Order($conn);

// الحصول على بيانات المستخدم الحالي
$user = $userModel->getById($_SESSION['user_id']);

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/public/profile.php');
    }

    // الحصول على البيانات المرسلة
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $phone = sanitize($_POST['phone']);
    $address = sanitize($_POST['address']);

    // التحقق من البريد الإلكتروني
    if ($email !== $user['email'] && $userModel->emailExists($email)) {
        $_SESSION['error'] = 'البريد الإلكتروني مستخدم بالفعل';
        redirect(APP_URL . '/public/profile.php');
    }

    // معالجة تحميل الصورة الشخصية
    $profile_image = $user['profile_image'] ?? 'default.png'; // الاحتفاظ بالصورة الحالية كقيمة افتراضية

    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2 ميجابايت

        if (!in_array($_FILES['profile_image']['type'], $allowed_types)) {
            $_SESSION['error'] = 'نوع الملف غير مسموح به. يرجى تحميل صورة بصيغة JPG أو PNG أو GIF';
            redirect(APP_URL . '/public/profile.php');
        }

        if ($_FILES['profile_image']['size'] > $max_size) {
            $_SESSION['error'] = 'حجم الصورة كبير جداً. الحد الأقصى هو 2 ميجابايت';
            redirect(APP_URL . '/public/profile.php');
        }

        // إنشاء اسم فريد للصورة
        $image_name = 'profile_' . $_SESSION['user_id'] . '_' . time() . '.' . pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION);
        $upload_dir = PUBLIC_PATH . '/uploads/users/';

        // التأكد من وجود المجلد
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $upload_path = $upload_dir . $image_name;

        if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
            $profile_image = $image_name;

            // حذف الصورة القديمة إذا كانت موجودة وليست الصورة الافتراضية
            if (!empty($user['profile_image']) && $user['profile_image'] !== 'default.png' && file_exists($upload_dir . $user['profile_image'])) {
                unlink($upload_dir . $user['profile_image']);
            }
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء تحميل الصورة';
            redirect(APP_URL . '/public/profile.php');
        }
    }

    // تحديث بيانات المستخدم
    $userModel->id = $_SESSION['user_id'];
    $userModel->name = $name;
    $userModel->email = $email;
    $userModel->phone = $phone;
    $userModel->address = $address;
    $userModel->profile_image = $profile_image;

    if ($userModel->updateProfile()) {
        // تحديث بيانات الجلسة
        $_SESSION['user_name'] = $name;
        $_SESSION['user_email'] = $email;
        $_SESSION['profile_image'] = $profile_image;

        $_SESSION['success'] = 'تم تحديث الملف الشخصي بنجاح';
    } else {
        $_SESSION['error'] = 'حدث خطأ أثناء تحديث الملف الشخصي';
    }

    redirect(APP_URL . '/public/profile.php#profile-tab');
}

// معالجة تغيير كلمة المرور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/public/profile.php#password-tab');
    }

    // الحصول على البيانات المرسلة
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // التحقق من كلمة المرور الحالية
    if (!password_verify($current_password, $user['password'])) {
        $_SESSION['error'] = 'كلمة المرور الحالية غير صحيحة';
        redirect(APP_URL . '/public/profile.php#password-tab');
    }

    // التحقق من تطابق كلمة المرور الجديدة
    if ($new_password !== $confirm_password) {
        $_SESSION['error'] = 'كلمة المرور الجديدة غير متطابقة';
        redirect(APP_URL . '/public/profile.php#password-tab');
    }

    // التحقق من طول كلمة المرور
    if (strlen($new_password) < 6) {
        $_SESSION['error'] = 'يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل';
        redirect(APP_URL . '/public/profile.php#password-tab');
    }

    // تحديث كلمة المرور
    $userModel->id = $_SESSION['user_id'];
    $userModel->password = $new_password;

    if ($userModel->updatePassword()) {
        $_SESSION['success'] = 'تم تغيير كلمة المرور بنجاح';
    } else {
        $_SESSION['error'] = 'حدث خطأ أثناء تغيير كلمة المرور';
    }

    redirect(APP_URL . '/public/profile.php#password-tab');
}

// الحصول على آخر الطلبات للمستخدم
$orders = $orderModel->getByUserId($_SESSION['user_id'], 5);

// التحقق من وجود جدول الإشعارات
$tableExists = false;
$result = $conn->query("SHOW TABLES LIKE 'notifications'");
if ($result && $result->num_rows > 0) {
    $tableExists = true;
}

// الحصول على الإشعارات غير المقروءة
$notifications = [];

if ($tableExists) {
    $sql = "SELECT * FROM notifications WHERE user_id = ? AND is_read = 0 ORDER BY created_at DESC LIMIT 10";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $notifications[] = $row;
        }
    }
}

// تضمين الرأس
include_once '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">الملف الشخصي</h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <?php
                        // تحديد مسار الصورة الشخصية
                        $profile_image = !empty($user['profile_image']) ? $user['profile_image'] : 'default.png';

                        // التحقق من وجود الصورة
                        $image_file = PUBLIC_PATH . '/uploads/users/' . $profile_image;
                        $image_path = APP_URL . '/public/uploads/users/' . $profile_image;

                        if (!file_exists($image_file) || empty($profile_image)) {
                            // إذا لم تكن الصورة موجودة، استخدم الصورة الافتراضية
                            $default_image_file = PUBLIC_PATH . '/public/img/default_user.png';
                            $image_path = APP_URL . '/public/img/default_user.png';

                            if (!file_exists($default_image_file)) {
                                // إذا لم تكن الصورة الافتراضية موجودة، استخدم صورة من الإنترنت
                                $image_path = 'https://via.placeholder.com/120x120?text=User';
                            }
                        }

                        // طباعة مسار الصورة للتصحيح
                        //echo "<!-- Image path: {$image_path}, File exists: " . (file_exists($image_file) ? 'Yes' : 'No') . " -->";
                        ?>
                        <img src="<?php echo $image_path; ?>" alt="<?php echo $user['name']; ?>" class="rounded-circle img-thumbnail" style="width: 120px; height: 120px; object-fit: cover;">
                    </div>
                    <h5><?php echo $user['name']; ?></h5>
                    <p class="text-muted">
                        <?php
                        switch ($user['role']) {
                            case 'admin':
                                echo '<span class="badge bg-danger">مدير</span>';
                                break;
                            case 'vendor':
                                echo '<span class="badge bg-success">بائع</span>';
                                break;
                            case 'customer':
                                echo '<span class="badge bg-primary">عميل</span>';
                                break;
                        }
                        ?>
                    </p>
                    <hr>
                    <ul class="nav nav-pills flex-column" id="profileTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active w-100 text-start mb-2" id="profile-tab-btn" data-bs-toggle="pill" data-bs-target="#profile-tab" type="button" role="tab" aria-controls="profile-tab" aria-selected="true">
                                <i class="fas fa-user"></i> الملف الشخصي
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link w-100 text-start mb-2" id="orders-tab-btn" data-bs-toggle="pill" data-bs-target="#orders-tab" type="button" role="tab" aria-controls="orders-tab" aria-selected="false">
                                <i class="fas fa-shopping-cart"></i> طلباتي
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link w-100 text-start mb-2" id="notifications-tab-btn" data-bs-toggle="pill" data-bs-target="#notifications-tab" type="button" role="tab" aria-controls="notifications-tab" aria-selected="false">
                                <i class="fas fa-bell"></i> الإشعارات
                                <?php if (count($notifications) > 0): ?>
                                    <span class="badge bg-danger"><?php echo count($notifications); ?></span>
                                <?php endif; ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link w-100 text-start mb-2" id="password-tab-btn" data-bs-toggle="pill" data-bs-target="#password-tab" type="button" role="tab" aria-controls="password-tab" aria-selected="false">
                                <i class="fas fa-lock"></i> تغيير كلمة المرور
                            </button>
                        </li>
                    </ul>

                    <hr>

                    <div class="d-grid gap-2 mt-3">
                        <?php if ($user['role'] === 'vendor'): ?>
                            <a href="<?php echo APP_URL; ?>/vendor/dashboard.php" class="btn btn-success">
                                <i class="fas fa-store"></i> لوحة تحكم المتجر
                            </a>
                        <?php endif; ?>
                        <?php if ($user['role'] === 'admin'): ?>
                            <a href="<?php echo APP_URL; ?>/admin/dashboard.php" class="btn btn-danger">
                                <i class="fas fa-tachometer-alt"></i> لوحة تحكم المدير
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo APP_URL; ?>/public/logout.php" class="btn btn-outline-danger">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-md-9">
            <div class="tab-content" id="profileTabsContent">
                <!-- تبويب الملف الشخصي -->
                <div class="tab-pane fade show active" id="profile-tab" role="tabpanel" aria-labelledby="profile-tab-btn">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">تعديل الملف الشخصي</h5>
                        </div>
                        <div class="card-body">
                            <form action="" method="POST" enctype="multipart/form-data">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="update_profile" value="1">

                                <div class="row">
                                    <div class="col-12 mb-4 text-center">
                                        <div class="position-relative d-inline-block">
                                            <?php
                                            // تحديد مسار الصورة الشخصية
                                            $profile_image = !empty($user['profile_image']) ? $user['profile_image'] : 'default.png';

                                            // التحقق من وجود الصورة، وإذا لم تكن موجودة استخدم الصورة الافتراضية
                                            $image_file = PUBLIC_PATH . '/uploads/profiles/' . $profile_image;

                                            if (file_exists($image_file)) {
                                                $image_path = APP_URL . '/public/uploads/profiles/' . $profile_image;
                                            } else {
                                                // استخدام الصورة الافتراضية
                                                $image_path = APP_URL . '/public/img/avatar.png';
                                            }
                                            ?>
                                            <img src="<?php echo $image_path; ?>" alt="<?php echo $user['name']; ?>" id="profile-image-preview" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;">
                                            <label for="profile_image" class="position-absolute bottom-0 end-0 bg-primary text-white rounded-circle p-2" style="cursor: pointer;">
                                                <i class="fas fa-camera"></i>
                                            </label>
                                            <input type="file" id="profile_image" name="profile_image" class="d-none" accept="image/*">
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">انقر على أيقونة الكاميرا لتغيير الصورة الشخصية</small>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" id="name" name="name" value="<?php echo $user['name']; ?>" required>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo $user['email']; ?>" required>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo $user['phone']; ?>">
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="role" class="form-label">نوع الحساب</label>
                                        <input type="text" class="form-control" id="role" value="<?php
                                            switch ($user['role']) {
                                                case 'admin':
                                                    echo 'مدير';
                                                    break;
                                                case 'vendor':
                                                    echo 'بائع';
                                                    break;
                                                case 'customer':
                                                    echo 'عميل';
                                                    break;
                                            }
                                        ?>" readonly>
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label for="address" class="form-label">العنوان</label>
                                        <textarea class="form-control" id="address" name="address" rows="3"><?php echo $user['address']; ?></textarea>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- تبويب الطلبات -->
                <div class="tab-pane fade" id="orders-tab" role="tabpanel" aria-labelledby="orders-tab-btn">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">آخر الطلبات</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($orders)): ?>
                                <div class="alert alert-info">
                                    <p class="mb-0">لا توجد طلبات حتى الآن.</p>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="<?php echo APP_URL; ?>/public/stores.php" class="btn btn-primary">تصفح المتاجر</a>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم الطلب</th>
                                                <th>المتجر</th>
                                                <th>المبلغ</th>
                                                <th>حالة الطلب</th>
                                                <th>تاريخ الطلب</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($orders as $order): ?>
                                                <tr>
                                                    <td>#<?php echo $order['id']; ?></td>
                                                    <td><?php echo $order['store_name']; ?></td>
                                                    <td><?php echo number_format($order['total_amount'], 2); ?> شيكل</td>
                                                    <td>
                                                        <?php
                                                        $statusClass = '';
                                                        $statusText = '';
                                                        switch ($order['status']) {
                                                            case 'new':
                                                                $statusClass = 'bg-primary';
                                                                $statusText = 'جديد';
                                                                break;
                                                            case 'processing':
                                                                $statusClass = 'bg-warning text-dark';
                                                                $statusText = 'قيد التجهيز';
                                                                break;
                                                            case 'ready':
                                                                $statusClass = 'bg-info';
                                                                $statusText = 'جاهز';
                                                                break;
                                                            case 'delivering':
                                                                $statusClass = 'bg-info';
                                                                $statusText = 'قيد التوصيل';
                                                                break;
                                                            case 'completed':
                                                                $statusClass = 'bg-success';
                                                                $statusText = 'مكتمل';
                                                                break;
                                                            case 'cancelled':
                                                                $statusClass = 'bg-danger';
                                                                $statusText = 'ملغي';
                                                                break;
                                                        }
                                                        echo '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';
                                                        ?>
                                                    </td>
                                                    <td><?php echo date('Y-m-d', strtotime($order['created_at'])); ?></td>
                                                    <td>
                                                        <a href="<?php echo APP_URL; ?>/public/order_details.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i> عرض
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="<?php echo APP_URL; ?>/public/orders.php" class="btn btn-primary">عرض جميع الطلبات</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- تبويب الإشعارات -->
                <div class="tab-pane fade" id="notifications-tab" role="tabpanel" aria-labelledby="notifications-tab-btn">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">الإشعارات</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($notifications)): ?>
                                <div class="alert alert-info">
                                    <p class="mb-0">لا توجد إشعارات جديدة.</p>
                                </div>
                            <?php else: ?>
                                <div class="list-group">
                                    <?php foreach ($notifications as $notification): ?>
                                        <div class="list-group-item list-group-item-action">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h5 class="mb-1"><?php echo $notification['title']; ?></h5>
                                                <small class="text-muted"><?php echo timeAgo($notification['created_at']); ?></small>
                                            </div>
                                            <p class="mb-1"><?php echo $notification['message']; ?></p>
                                            <div class="d-flex justify-content-end">
                                                <a href="<?php echo APP_URL; ?>/public/mark_notification.php?id=<?php echo $notification['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-check"></i> تحديد كمقروء
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="<?php echo APP_URL; ?>/public/notifications.php" class="btn btn-primary">عرض جميع الإشعارات</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- تبويب تغيير كلمة المرور -->
                <div class="tab-pane fade" id="password-tab" role="tabpanel" aria-labelledby="password-tab-btn">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">تغيير كلمة المرور</h5>
                        </div>
                        <div class="card-body">
                            <form action="" method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="change_password" value="1">

                                <div class="mb-3">
                                    <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                </div>

                                <div class="mb-3">
                                    <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                    <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                                </div>

                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">تغيير كلمة المرور</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// دالة لحساب الوقت المنقضي
function timeAgo($datetime) {
    $now = new DateTime();
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    if ($diff->y > 0) {
        return $diff->y . ' سنة' . ($diff->y > 1 ? '' : '') . ' مضت';
    } elseif ($diff->m > 0) {
        return $diff->m . ' شهر' . ($diff->m > 1 ? '' : '') . ' مضت';
    } elseif ($diff->d > 0) {
        return $diff->d . ' يوم' . ($diff->d > 1 ? '' : '') . ' مضت';
    } elseif ($diff->h > 0) {
        return $diff->h . ' ساعة' . ($diff->h > 1 ? '' : '') . ' مضت';
    } elseif ($diff->i > 0) {
        return $diff->i . ' دقيقة' . ($diff->i > 1 ? '' : '') . ' مضت';
    } else {
        return 'الآن';
    }
}

// إضافة JavaScript لمعاينة الصورة قبل التحميل
?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التبويبات
    var triggerTabList = [].slice.call(document.querySelectorAll('#profileTabs button'));
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl);

        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
        });
    });

    // معاينة الصورة الشخصية قبل التحميل
    const profileImageInput = document.getElementById('profile_image');
    const profileImagePreview = document.getElementById('profile-image-preview');

    if (profileImageInput && profileImagePreview) {
        profileImageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    profileImagePreview.src = e.target.result;
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    }

    // تفعيل التبويبات عند النقر على الروابط
    const urlHash = window.location.hash;
    if (urlHash) {
        // التعامل مع التبويبات باستخدام Bootstrap 5
        const tabId = urlHash.replace('#', '');
        const tabEl = document.getElementById(tabId);

        if (tabEl) {
            const tab = new bootstrap.Tab(document.getElementById(`${tabId}-btn`));
            tab.show();
        }
    }

    // تحسين تجربة المستخدم في نموذج تغيير كلمة المرور
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');

    if (newPasswordInput && confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            if (this.value !== newPasswordInput.value) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });

        newPasswordInput.addEventListener('input', function() {
            if (confirmPasswordInput.value !== '' && this.value !== confirmPasswordInput.value) {
                confirmPasswordInput.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                confirmPasswordInput.setCustomValidity('');
            }
        });
    }
});
</script>

<?php
// تضمين التذييل
include_once '../includes/footer.php';
?>
