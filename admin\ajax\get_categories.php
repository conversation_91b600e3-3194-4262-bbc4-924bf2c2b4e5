<?php
// تضمين ملف التكوين
require_once '../../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['error' => 'غير مصرح']);
    exit;
}

// التحقق من وجود معرف المتجر
if (!isset($_GET['store_id']) || empty($_GET['store_id'])) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['error' => 'معرف المتجر مطلوب']);
    exit;
}

$storeId = intval($_GET['store_id']);

// الحصول على فئات المتجر
$sql = "SELECT * FROM product_categories WHERE store_id = ? ORDER BY name";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $storeId);
$stmt->execute();
$result = $stmt->get_result();

$categories = [];
while ($row = $result->fetch_assoc()) {
    $categories[] = $row;
}

// إعداد رأس الاستجابة
header('Content-Type: application/json');
echo json_encode($categories);
?>
