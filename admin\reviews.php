<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المتجر
require_once '../models/Store.php';

// إنشاء كائن المتجر
$storeModel = new Store($conn);

// معالجة الإجراءات
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $reviewId = isset($_GET['id']) ? intval($_GET['id']) : 0;

    // التحقق من وجود التقييم
    $sql = "SELECT * FROM reviews WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $reviewId);
    $stmt->execute();
    $review = $stmt->get_result()->fetch_assoc();

    if (!$review) {
        $_SESSION['error'] = 'التقييم غير موجود';
        redirect(APP_URL . '/admin/reviews.php');
    }

    // تنفيذ الإجراء المطلوب
    switch ($action) {
        case 'delete':
            // حذف التقييم
            $sql = "DELETE FROM reviews WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $reviewId);

            if ($stmt->execute()) {
                $_SESSION['success'] = 'تم حذف التقييم بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف التقييم';
            }
            break;
    }

    redirect(APP_URL . '/admin/reviews.php');
}

// الحصول على معايير التصفية
$storeId = isset($_GET['store_id']) ? intval($_GET['store_id']) : null;
$rating = isset($_GET['rating']) ? intval($_GET['rating']) : null;
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : null;
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : null;

// الحصول على قائمة التقييمات
$sql = "SELECT r.*, p.name as product_name, u.name as user_name, s.name as store_name
        FROM reviews r
        JOIN users u ON r.user_id = u.id
        JOIN products p ON r.product_id = p.id
        JOIN stores s ON p.store_id = s.id
        WHERE 1=1";

// إضافة معايير التصفية
$params = [];
$types = "";

if ($storeId) {
    $sql .= " AND s.id = ?";
    $params[] = $storeId;
    $types .= "i";
}

if ($rating) {
    $sql .= " AND r.rating = ?";
    $params[] = $rating;
    $types .= "i";
}

if ($dateFrom) {
    $sql .= " AND DATE(r.created_at) >= ?";
    $params[] = $dateFrom;
    $types .= "s";
}

if ($dateTo) {
    $sql .= " AND DATE(r.created_at) <= ?";
    $params[] = $dateTo;
    $types .= "s";
}

$sql .= " ORDER BY r.created_at DESC";

// تنفيذ الاستعلام
$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
$reviews = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $reviews[] = $row;
    }
}

// الحصول على قائمة المتاجر للفلتر
$stores = $storeModel->getAll(null, true);

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة التقييمات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>

            <!-- فلتر التقييمات -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">فلتر التقييمات</h5>
                </div>
                <div class="card-body">
                    <form action="" method="GET">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="store_id" class="form-label">المتجر</label>
                                <select name="store_id" id="store_id" class="form-select">
                                    <option value="">جميع المتاجر</option>
                                    <?php foreach ($stores as $store): ?>
                                        <option value="<?php echo $store['id']; ?>" <?php echo $storeId == $store['id'] ? 'selected' : ''; ?>>
                                            <?php echo $store['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="rating" class="form-label">التقييم</label>
                                <select name="rating" id="rating" class="form-select">
                                    <option value="">جميع التقييمات</option>
                                    <option value="5" <?php echo $rating === 5 ? 'selected' : ''; ?>>5 نجوم</option>
                                    <option value="4" <?php echo $rating === 4 ? 'selected' : ''; ?>>4 نجوم</option>
                                    <option value="3" <?php echo $rating === 3 ? 'selected' : ''; ?>>3 نجوم</option>
                                    <option value="2" <?php echo $rating === 2 ? 'selected' : ''; ?>>2 نجوم</option>
                                    <option value="1" <?php echo $rating === 1 ? 'selected' : ''; ?>>1 نجمة</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" name="date_from" id="date_from" class="form-control" value="<?php echo $dateFrom; ?>">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" name="date_to" id="date_to" class="form-control" value="<?php echo $dateTo; ?>">
                            </div>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">تصفية</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة التقييمات -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">قائمة التقييمات</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($reviews)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد تقييمات تطابق معايير البحث.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>العميل</th>
                                        <th>المتجر</th>
                                        <th>المنتج</th>
                                        <th>التقييم</th>
                                        <th>التعليق</th>
                                        <th>تاريخ التقييم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($reviews as $review): ?>
                                        <tr>
                                            <td><?php echo $review['id']; ?></td>
                                            <td><?php echo $review['user_name']; ?></td>
                                            <td><?php echo $review['store_name']; ?></td>
                                            <td>
                                                <a href="<?php echo APP_URL; ?>/admin/product_details.php?id=<?php echo $review['product_id']; ?>">
                                                    <?php echo $review['product_name']; ?>
                                                </a>
                                            </td>
                                            <td>
                                                <?php
                                                for ($i = 1; $i <= 5; $i++) {
                                                    if ($i <= $review['rating']) {
                                                        echo '<i class="fas fa-star text-warning"></i>';
                                                    } else {
                                                        echo '<i class="far fa-star text-warning"></i>';
                                                    }
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                if (!empty($review['comment'])) {
                                                    echo substr($review['comment'], 0, 50) . (strlen($review['comment']) > 50 ? '...' : '');
                                                    echo '<button type="button" class="btn btn-sm btn-link view-comment" data-bs-toggle="modal" data-bs-target="#commentModal" data-comment="' . htmlspecialchars($review['comment']) . '">عرض المزيد</button>';
                                                } else {
                                                    echo '<span class="text-muted">لا يوجد تعليق</span>';
                                                }
                                                ?>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($review['created_at'])); ?></td>
                                            <td>
                                                <a href="<?php echo APP_URL; ?>/admin/reviews.php?action=delete&id=<?php echo $review['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا التقييم؟')">
                                                    <i class="fas fa-trash"></i> حذف
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- إحصائيات التقييمات -->
            <div class="row mt-4">
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">متوسط التقييمات حسب المتجر</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="storeRatingsChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">توزيع التقييمات</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="ratingsDistributionChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض التعليق كاملاً -->
<div class="modal fade" id="commentModal" tabindex="-1" aria-labelledby="commentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="commentModalLabel">تعليق العميل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="commentText"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript لإنشاء الرسوم البيانية وعرض التعليقات -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // عرض التعليق كاملاً
    const viewCommentButtons = document.querySelectorAll('.view-comment');
    viewCommentButtons.forEach(button => {
        button.addEventListener('click', function() {
            const comment = this.getAttribute('data-comment');
            document.getElementById('commentText').textContent = comment;
        });
    });

    // رسم بياني لمتوسط التقييمات حسب المتجر
    const storeRatingsCtx = document.getElementById('storeRatingsChart').getContext('2d');
    const storeRatingsChart = new Chart(storeRatingsCtx, {
        type: 'bar',
        data: {
            labels: [
                <?php
                $sql = "SELECT s.name, AVG(r.rating) as avg_rating
                        FROM stores s
                        JOIN products p ON p.store_id = s.id
                        LEFT JOIN reviews r ON p.id = r.product_id
                        GROUP BY s.id
                        HAVING avg_rating IS NOT NULL
                        ORDER BY avg_rating DESC
                        LIMIT 10";
                $result = $conn->query($sql);

                if ($result && $result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        echo "'" . $row['name'] . "', ";
                    }
                }
                ?>
            ],
            datasets: [{
                label: 'متوسط التقييم',
                data: [
                    <?php
                    $result->data_seek(0); // إعادة مؤشر النتائج إلى البداية
                    if ($result && $result->num_rows > 0) {
                        while ($row = $result->fetch_assoc()) {
                            echo round($row['avg_rating'], 1) . ', ';
                        }
                    }
                    ?>
                ],
                backgroundColor: 'rgba(255, 193, 7, 0.7)',
                borderColor: 'rgba(255, 193, 7, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 5
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // رسم بياني لتوزيع التقييمات
    const ratingsDistributionCtx = document.getElementById('ratingsDistributionChart').getContext('2d');
    const ratingsDistributionChart = new Chart(ratingsDistributionCtx, {
        type: 'pie',
        data: {
            labels: ['5 نجوم', '4 نجوم', '3 نجوم', '2 نجوم', '1 نجمة'],
            datasets: [{
                data: [
                    <?php
                    for ($i = 5; $i >= 1; $i--) {
                        $sql = "SELECT COUNT(*) as count FROM reviews WHERE rating = $i";
                        $result = $conn->query($sql);
                        $row = $result->fetch_assoc();
                        echo $row['count'] . ', ';
                    }
                    ?>
                ],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.7)',
                    'rgba(23, 162, 184, 0.7)',
                    'rgba(255, 193, 7, 0.7)',
                    'rgba(253, 126, 20, 0.7)',
                    'rgba(220, 53, 69, 0.7)'
                ],
                borderColor: [
                    'rgba(40, 167, 69, 1)',
                    'rgba(23, 162, 184, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(253, 126, 20, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
});
</script>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
