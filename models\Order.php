<?php
/**
 * Order Model
 *
 * Handles all order-related operations
 */
class Order {
    private $conn;
    private $table = 'orders';

    // Order properties
    public $id;
    public $user_id;
    public $store_id;
    public $total_amount;
    public $status;
    public $payment_method;
    public $payment_status;
    public $address;
    public $phone;
    public $notes;
    public $created_at;
    public $updated_at;

    /**
     * Constructor
     *
     * @param mysqli $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create a new order
     *
     * @return boolean
     */
    public function create() {
        // Create query
        $query = "INSERT INTO " . $this->table . "
                  SET user_id = ?,
                      store_id = ?,
                      total_amount = ?,
                      status = ?,
                      payment_method = ?,
                      payment_status = ?,
                      address = ?,
                      phone = ?,
                      notes = ?,
                      created_at = NOW(),
                      updated_at = NOW()";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->user_id = htmlspecialchars(strip_tags($this->user_id));
        $this->store_id = htmlspecialchars(strip_tags($this->store_id));
        $this->total_amount = htmlspecialchars(strip_tags($this->total_amount));
        $this->status = htmlspecialchars(strip_tags($this->status));
        $this->payment_method = htmlspecialchars(strip_tags($this->payment_method));
        $this->payment_status = htmlspecialchars(strip_tags($this->payment_status));
        $this->address = htmlspecialchars(strip_tags($this->address));
        $this->phone = htmlspecialchars(strip_tags($this->phone));
        $this->notes = htmlspecialchars(strip_tags($this->notes));

        // Bind data
        $stmt->bind_param("iidssssss",
            $this->user_id,
            $this->store_id,
            $this->total_amount,
            $this->status,
            $this->payment_method,
            $this->payment_status,
            $this->address,
            $this->phone,
            $this->notes
        );

        // Execute query
        if($stmt->execute()) {
            $this->id = $this->conn->insert_id;
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Get order by ID
     *
     * @param int $id
     * @return array|boolean
     */
    public function getById($id) {
        // Create query
        $query = "SELECT o.*, s.name as store_name, u.name as user_name
                  FROM " . $this->table . " o
                  JOIN stores s ON o.store_id = s.id
                  JOIN users u ON o.user_id = u.id
                  WHERE o.id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind ID
        $stmt->bind_param("i", $id);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        if($result->num_rows > 0) {
            return $result->fetch_assoc();
        } else {
            return false;
        }
    }

    /**
     * Get orders by user ID
     *
     * @param int $userId
     * @param int $limit Optional limit of orders to return
     * @return array
     */
    public function getByUserId($userId, $limit = null) {
        // Create query
        $query = "SELECT o.*, s.name as store_name
                  FROM " . $this->table . " o
                  JOIN stores s ON o.store_id = s.id
                  WHERE o.user_id = ?
                  ORDER BY o.created_at DESC";

        // Add limit if provided
        if ($limit) {
            $query .= " LIMIT " . intval($limit);
        }

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind user ID
        $stmt->bind_param("i", $userId);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        $orders = [];

        while($row = $result->fetch_assoc()) {
            $orders[] = $row;
        }

        return $orders;
    }

    /**
     * Get orders by store ID
     *
     * @param int $storeId
     * @param string $status Optional status filter
     * @return array
     */
    public function getByStoreId($storeId, $status = null) {
        // Create query
        $query = "SELECT o.*, u.name as user_name
                  FROM " . $this->table . " o
                  JOIN users u ON o.user_id = u.id
                  WHERE o.store_id = ?";

        // Add status filter if provided
        if($status) {
            $query .= " AND o.status = ?";
        }

        $query .= " ORDER BY o.created_at DESC";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind parameters
        if($status) {
            $stmt->bind_param("is", $storeId, $status);
        } else {
            $stmt->bind_param("i", $storeId);
        }

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        $orders = [];

        while($row = $result->fetch_assoc()) {
            $orders[] = $row;
        }

        return $orders;
    }

    /**
     * Update order status
     *
     * @param int $id
     * @param string $status
     * @param int $storeId
     * @return boolean
     */
    public function updateStatus($id, $status, $storeId) {
        // Create query
        $query = "UPDATE " . $this->table . "
                  SET status = ?,
                      updated_at = NOW()
                  WHERE id = ? AND store_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $status = htmlspecialchars(strip_tags($status));

        // Bind data
        $stmt->bind_param("sii", $status, $id, $storeId);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Update payment status
     *
     * @param int $id
     * @param string $paymentStatus
     * @return boolean
     */
    public function updatePaymentStatus($id, $paymentStatus) {
        // Create query
        $query = "UPDATE " . $this->table . "
                  SET payment_status = ?,
                      updated_at = NOW()
                  WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $paymentStatus = htmlspecialchars(strip_tags($paymentStatus));

        // Bind data
        $stmt->bind_param("si", $paymentStatus, $id);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Get order items
     *
     * @param int $orderId
     * @return array
     */
    public function getOrderItems($orderId) {
        // Create query
        $query = "SELECT oi.*, p.name as product_name, p.image as product_image
                  FROM order_items oi
                  JOIN products p ON oi.product_id = p.id
                  WHERE oi.order_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind order ID
        $stmt->bind_param("i", $orderId);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        $items = [];

        while($row = $result->fetch_assoc()) {
            $items[] = $row;
        }

        return $items;
    }

    /**
     * Add order item
     *
     * @param int $orderId
     * @param int $productId
     * @param int $quantity
     * @param float $price
     * @return boolean
     */
    public function addOrderItem($orderId, $productId, $quantity, $price) {
        try {
            // Try to insert with created_at column
            $query = "INSERT INTO order_items
                      SET order_id = ?,
                          product_id = ?,
                          quantity = ?,
                          price = ?,
                          created_at = NOW()";

            // Prepare statement
            $stmt = $this->conn->prepare($query);

            // Bind data
            $stmt->bind_param("iiid", $orderId, $productId, $quantity, $price);

            // Execute query
            return $stmt->execute();
        } catch (mysqli_sql_exception $e) {
            // If error is about created_at column, try without it
            if (strpos($e->getMessage(), 'created_at') !== false) {
                try {
                    // Create query without created_at
                    $query = "INSERT INTO order_items
                              SET order_id = ?,
                                  product_id = ?,
                                  quantity = ?,
                                  price = ?";

                    // Prepare statement
                    $stmt = $this->conn->prepare($query);

                    // Bind data
                    $stmt->bind_param("iiid", $orderId, $productId, $quantity, $price);

                    // Execute query
                    if($stmt->execute()) {
                        return true;
                    }
                } catch (Exception $e2) {
                    // Print error if something goes wrong
                    printf("Error: %s.\n", $e2->getMessage());
                    return false;
                }
            } else {
                // Print error if something goes wrong
                printf("Error: %s.\n", $e->getMessage());
                return false;
            }
        }

        return false;
    }

    /**
     * Get all orders (admin)
     *
     * @param string $status Optional status filter
     * @return array
     */
    public function getAllOrders($status = null) {
        // Create query
        $query = "SELECT o.*, s.name as store_name, u.name as user_name
                  FROM " . $this->table . " o
                  JOIN stores s ON o.store_id = s.id
                  JOIN users u ON o.user_id = u.id";

        // Add status filter if provided
        if($status) {
            $query .= " WHERE o.status = '" . $this->conn->real_escape_string($status) . "'";
        }

        $query .= " ORDER BY o.created_at DESC";

        // Execute query
        $result = $this->conn->query($query);

        $orders = [];

        while($row = $result->fetch_assoc()) {
            $orders[] = $row;
        }

        return $orders;
    }

    /**
     * Get order statistics
     *
     * @param int $storeId Optional store filter
     * @return array
     */
    public function getStatistics($storeId = null) {
        // Create base query
        $baseQuery = "SELECT COUNT(*) as count FROM " . $this->table;

        // Add store filter if provided
        $storeFilter = "";
        if($storeId) {
            $storeFilter = " WHERE store_id = " . intval($storeId);
        }

        // Get total orders
        $query = $baseQuery . $storeFilter;
        $result = $this->conn->query($query);
        $totalOrders = $result->fetch_assoc()['count'];

        // Get orders by status
        $statuses = ['new', 'processing', 'ready', 'delivering', 'completed', 'cancelled'];
        $ordersByStatus = [];

        foreach($statuses as $status) {
            $query = $baseQuery . ($storeFilter ? $storeFilter . " AND " : " WHERE ") . "status = '" . $status . "'";
            $result = $this->conn->query($query);
            $ordersByStatus[$status] = $result->fetch_assoc()['count'];
        }

        // Get total sales
        $query = "SELECT SUM(total_amount) as total FROM " . $this->table .
                 ($storeFilter ? $storeFilter . " AND " : " WHERE ") .
                 "payment_status = 'paid'";
        $result = $this->conn->query($query);
        $totalSales = $result->fetch_assoc()['total'] ?? 0;

        return [
            'total_orders' => $totalOrders,
            'orders_by_status' => $ordersByStatus,
            'total_sales' => $totalSales
        ];
    }
}
