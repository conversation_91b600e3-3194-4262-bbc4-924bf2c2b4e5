<?php
/**
 * StoreReview Model
 */
class StoreReview {
    // Database connection
    private $conn;
    
    // Table name
    private $table = 'store_reviews';
    
    // Properties
    public $id;
    public $user_id;
    public $store_id;
    public $rating;
    public $comment;
    public $reply;
    
    /**
     * Constructor
     * 
     * @param object $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }
    
    /**
     * Get all store reviews
     * 
     * @param int $limit Optional limit
     * @return array
     */
    public function getAll($limit = null) {
        // Create query
        $query = "SELECT r.*, u.name as user_name, s.name as store_name, s.logo as store_logo 
                  FROM " . $this->table . " r
                  JOIN users u ON r.user_id = u.id
                  JOIN stores s ON r.store_id = s.id
                  ORDER BY r.created_at DESC";
        
        // Add limit if provided
        if($limit) {
            $query .= " LIMIT " . intval($limit);
        }
        
        // Execute query
        $result = $this->conn->query($query);
        
        $reviews = [];
        
        while($row = $result->fetch_assoc()) {
            $reviews[] = $row;
        }
        
        return $reviews;
    }
    
    /**
     * Get review by ID
     * 
     * @param int $id
     * @return array|boolean
     */
    public function getById($id) {
        // Create query
        $query = "SELECT r.*, u.name as user_name, s.name as store_name, s.logo as store_logo 
                  FROM " . $this->table . " r
                  JOIN users u ON r.user_id = u.id
                  JOIN stores s ON r.store_id = s.id
                  WHERE r.id = ?";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind ID
        $stmt->bind_param("i", $id);
        
        // Execute query
        $stmt->execute();
        
        // Get result
        $result = $stmt->get_result();
        
        if($result->num_rows > 0) {
            return $result->fetch_assoc();
        } else {
            return false;
        }
    }
    
    /**
     * Get reviews by store ID
     * 
     * @param int $storeId
     * @param int $limit Optional limit
     * @return array
     */
    public function getByStoreId($storeId, $limit = null) {
        // Create query
        $query = "SELECT r.*, u.name as user_name, u.profile_image as user_image 
                  FROM " . $this->table . " r
                  JOIN users u ON r.user_id = u.id
                  WHERE r.store_id = ?
                  ORDER BY r.created_at DESC";
        
        // Add limit if provided
        if($limit) {
            $query .= " LIMIT " . intval($limit);
        }
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind store ID
        $stmt->bind_param("i", $storeId);
        
        // Execute query
        $stmt->execute();
        
        // Get result
        $result = $stmt->get_result();
        
        $reviews = [];
        
        while($row = $result->fetch_assoc()) {
            $reviews[] = $row;
        }
        
        return $reviews;
    }
    
    /**
     * Get reviews by user ID
     * 
     * @param int $userId
     * @param int $limit Optional limit
     * @return array
     */
    public function getByUserId($userId, $limit = null) {
        // Create query
        $query = "SELECT r.*, s.name as store_name, s.logo as store_logo, s.id as store_id 
                  FROM " . $this->table . " r
                  JOIN stores s ON r.store_id = s.id
                  WHERE r.user_id = ?
                  ORDER BY r.created_at DESC";
        
        // Add limit if provided
        if($limit) {
            $query .= " LIMIT " . intval($limit);
        }
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind user ID
        $stmt->bind_param("i", $userId);
        
        // Execute query
        $stmt->execute();
        
        // Get result
        $result = $stmt->get_result();
        
        $reviews = [];
        
        while($row = $result->fetch_assoc()) {
            $reviews[] = $row;
        }
        
        return $reviews;
    }
    
    /**
     * Check if user has already reviewed a store
     * 
     * @param int $userId
     * @param int $storeId
     * @return boolean
     */
    public function hasUserReviewedStore($userId, $storeId) {
        // Create query
        $query = "SELECT COUNT(*) as count FROM " . $this->table . " 
                  WHERE user_id = ? AND store_id = ?";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind parameters
        $stmt->bind_param("ii", $userId, $storeId);
        
        // Execute query
        $stmt->execute();
        
        // Get result
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row['count'] > 0;
    }
    
    /**
     * Create review
     * 
     * @return boolean
     */
    public function create() {
        // Create query
        $query = "INSERT INTO " . $this->table . " 
                  (user_id, store_id, rating, comment, created_at, updated_at) 
                  VALUES (?, ?, ?, ?, NOW(), NOW())";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $this->comment = htmlspecialchars(strip_tags($this->comment));
        
        // Bind data
        $stmt->bind_param("iiis", 
            $this->user_id, 
            $this->store_id, 
            $this->rating, 
            $this->comment
        );
        
        // Execute query
        if($stmt->execute()) {
            $this->id = $this->conn->insert_id;
            return true;
        }
        
        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);
        
        return false;
    }
    
    /**
     * Update review
     * 
     * @return boolean
     */
    public function update() {
        // Create query
        $query = "UPDATE " . $this->table . " 
                  SET rating = ?, comment = ?, updated_at = NOW() 
                  WHERE id = ? AND user_id = ?";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $this->comment = htmlspecialchars(strip_tags($this->comment));
        
        // Bind data
        $stmt->bind_param("isii", 
            $this->rating, 
            $this->comment, 
            $this->id, 
            $this->user_id
        );
        
        // Execute query
        if($stmt->execute()) {
            return true;
        }
        
        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);
        
        return false;
    }
    
    /**
     * Add reply to review
     * 
     * @param int $id Review ID
     * @param string $reply Reply text
     * @return boolean
     */
    public function addReply($id, $reply) {
        // Create query
        $query = "UPDATE " . $this->table . " 
                  SET reply = ?, updated_at = NOW() 
                  WHERE id = ?";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $reply = htmlspecialchars(strip_tags($reply));
        
        // Bind data
        $stmt->bind_param("si", $reply, $id);
        
        // Execute query
        if($stmt->execute()) {
            return true;
        }
        
        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);
        
        return false;
    }
    
    /**
     * Delete review
     * 
     * @param int $id
     * @return boolean
     */
    public function delete($id) {
        // Create query
        $query = "DELETE FROM " . $this->table . " WHERE id = ?";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind ID
        $stmt->bind_param("i", $id);
        
        // Execute query
        if($stmt->execute()) {
            return true;
        }
        
        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);
        
        return false;
    }
    
    /**
     * Get average rating for store
     * 
     * @param int $storeId
     * @return float
     */
    public function getAverageRating($storeId) {
        // Create query
        $query = "SELECT AVG(rating) as avg_rating FROM " . $this->table . " WHERE store_id = ?";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind store ID
        $stmt->bind_param("i", $storeId);
        
        // Execute query
        $stmt->execute();
        
        // Get result
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row['avg_rating'] ? round($row['avg_rating'], 1) : 0;
    }
    
    /**
     * Get rating distribution for store
     * 
     * @param int $storeId
     * @return array
     */
    public function getRatingDistribution($storeId) {
        // Create query
        $query = "SELECT rating, COUNT(*) as count FROM " . $this->table . " 
                  WHERE store_id = ? 
                  GROUP BY rating 
                  ORDER BY rating DESC";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind store ID
        $stmt->bind_param("i", $storeId);
        
        // Execute query
        $stmt->execute();
        
        // Get result
        $result = $stmt->get_result();
        
        $distribution = [
            5 => 0,
            4 => 0,
            3 => 0,
            2 => 0,
            1 => 0
        ];
        
        while($row = $result->fetch_assoc()) {
            $distribution[$row['rating']] = $row['count'];
        }
        
        return $distribution;
    }
}
?>
