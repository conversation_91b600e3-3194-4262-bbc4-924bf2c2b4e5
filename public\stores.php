<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelo de tienda
require_once '../models/Store.php';

// Inicializar el modelo de tienda
$storeModel = new Store($conn);

// Obtener categoría seleccionada (si existe)
$categoryId = isset($_GET['category']) ? intval($_GET['category']) : null;

// Obtener término de búsqueda (si existe)
$searchTerm = isset($_GET['search']) ? sanitize($_GET['search']) : null;

// Obtener tiendas
if($searchTerm) {
    // Buscar tiendas por término
    $stores = $storeModel->search($searchTerm);
    $pageTitle = 'نتائج البحث: ' . $searchTerm;
} else {
    // Obtener tiendas por categoría o todas
    $stores = $storeModel->getAll($categoryId);
    $pageTitle = $categoryId ? 'المتاجر في الفئة' : 'جميع المتاجر';
}

// Obtener categorías de tiendas
$sql = "SELECT * FROM store_categories ORDER BY name";
$result = $conn->query($sql);
$categories = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
}

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="row">
    <!-- Barra lateral de categorías -->
    <div class="col-md-3 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">الفئات</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="<?php echo APP_URL; ?>/public/stores.php" class="list-group-item list-group-item-action <?php echo !$categoryId ? 'active' : ''; ?>">
                        جميع المتاجر
                    </a>
                    <?php foreach($categories as $category): ?>
                        <a href="<?php echo APP_URL; ?>/public/stores.php?category=<?php echo $category['id']; ?>" class="list-group-item list-group-item-action <?php echo $categoryId == $category['id'] ? 'active' : ''; ?>">
                            <i class="<?php echo $category['icon']; ?> me-2"></i> <?php echo $category['name']; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Formulario de búsqueda -->
        <div class="card shadow-sm mt-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">بحث</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo APP_URL; ?>/public/stores.php" method="GET">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" placeholder="ابحث عن متجر..." value="<?php echo $searchTerm ?? ''; ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Lista de tiendas -->
    <div class="col-md-9">
        <h2 class="mb-4"><?php echo $pageTitle; ?></h2>

        <?php if(empty($stores)): ?>
            <div class="alert alert-info">
                <p class="mb-0">لم يتم العثور على متاجر</p>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach($stores as $store): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <?php
                            // التحقق من وجود صورة الغلاف
                            $hasBanner = false;
                            if (!empty($store['banner'])) {
                                $bannerPath = ROOT_PATH . '/public/uploads/stores/' . $store['banner'];
                                if (file_exists($bannerPath)) {
                                    $hasBanner = true;
                                    $bannerUrl = APP_URL . '/public/uploads/stores/' . $store['banner'];
                                }
                            }

                            if ($hasBanner): ?>
                                <img src="<?php echo $bannerUrl; ?>" class="card-img-top store-banner" alt="<?php echo $store['name']; ?>">
                            <?php endif; ?>
                            <div class="card-body text-center">
                                <?php
                                // التحقق من وجود شعار المتجر
                                $storeLogoPath = ROOT_PATH . '/public/uploads/stores/' . $store['logo'];
                                $storeLogoUrl = APP_URL . '/public/uploads/stores/' . $store['logo'];

                                if (!file_exists($storeLogoPath) || empty($store['logo'])) {
                                    // إذا لم يكن الشعار موجوداً، استخدم الشعار الافتراضي
                                    $defaultStoreLogoPath = ROOT_PATH . '/public/img/default_store.png';
                                    $storeLogoUrl = APP_URL . '/public/img/default_store.png';

                                    if (!file_exists($defaultStoreLogoPath)) {
                                        // إذا لم يكن الشعار الافتراضي موجوداً، استخدم صورة من الإنترنت
                                        $storeLogoUrl = 'https://via.placeholder.com/100x100?text=Store';
                                    }
                                }
                                ?>
                                <img src="<?php echo $storeLogoUrl; ?>" class="store-logo mb-3" alt="<?php echo $store['name']; ?>">
                                <h5 class="card-title"><?php echo $store['name']; ?></h5>
                                <?php if (isset($store['category_name'])): ?>
                                    <p class="card-text text-muted"><?php echo $store['category_name']; ?></p>
                                <?php else: ?>
                                    <?php
                                    // الحصول على اسم الفئة من جدول الفئات
                                    $categoryName = '';
                                    if (isset($store['category_id'])) {
                                        $sql = "SELECT name FROM store_categories WHERE id = ?";
                                        $stmt = $conn->prepare($sql);
                                        $stmt->bind_param("i", $store['category_id']);
                                        $stmt->execute();
                                        $result = $stmt->get_result();
                                        if ($result && $result->num_rows > 0) {
                                            $categoryName = $result->fetch_assoc()['name'];
                                        }
                                    }
                                    ?>
                                    <p class="card-text text-muted"><?php echo $categoryName; ?></p>
                                <?php endif; ?>
                                <p class="card-text small"><?php echo substr($store['description'], 0, 100) . '...'; ?></p>
                            </div>
                            <div class="card-footer bg-white">
                                <a href="<?php echo APP_URL; ?>/public/store.php?id=<?php echo $store['id']; ?>" class="btn btn-primary w-100">تصفح المتجر</a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
