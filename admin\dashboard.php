<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">لوحة التحكم</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="<?php echo APP_URL; ?>/database/update_tables.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-database me-1"></i> تحديث قاعدة البيانات
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-secondary">تصدير</button>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle">
                        <i class="fas fa-calendar"></i> هذا الأسبوع
                    </button>
                </div>
            </div>

            <!-- ملخص الإحصائيات -->
            <div class="row" data-aos="fade-up">
                <div class="col-md-3 mb-4">
                    <div class="stats-card primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">المستخدمين</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM users";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-users fa-2x icon"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a href="users.php" class="text-decoration-none">عرض التفاصيل</a>
                            <i class="fas fa-arrow-circle-right"></i>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="stats-card success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">المتاجر</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM stores";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-store fa-2x icon"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a href="stores.php" class="text-decoration-none">عرض التفاصيل</a>
                            <i class="fas fa-arrow-circle-right"></i>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="stats-card warning">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">المنتجات</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM products";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-shopping-basket fa-2x icon"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a href="products.php" class="text-decoration-none">عرض التفاصيل</a>
                            <i class="fas fa-arrow-circle-right"></i>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="stats-card danger">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">الطلبات</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $sql = "SELECT COUNT(*) as count FROM orders";
                                        $result = $conn->query($sql);
                                        $row = $result->fetch_assoc();
                                        echo $row['count'];
                                        ?>
                                    </h2>
                                </div>
                                <i class="fas fa-shopping-cart fa-2x icon"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a href="orders.php" class="text-decoration-none">عرض التفاصيل</a>
                            <i class="fas fa-arrow-circle-right"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- طلبات المتاجر الجديدة -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">طلبات المتاجر الجديدة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم المتجر</th>
                                    <th>المالك</th>
                                    <th>الفئة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $sql = "SELECT s.*, sc.name as category_name, u.name as owner_name, u.email as owner_email
                                        FROM stores s
                                        JOIN store_categories sc ON s.category_id = sc.id
                                        JOIN users u ON s.user_id = u.id
                                        WHERE s.is_active = 0
                                        ORDER BY s.created_at DESC
                                        LIMIT 5";
                                $result = $conn->query($sql);

                                if ($result && $result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()) {
                                        echo '<tr>';
                                        echo '<td>' . $row['id'] . '</td>';
                                        echo '<td>' . $row['name'] . '</td>';
                                        echo '<td>' . $row['owner_name'] . '<br><small class="text-muted">' . $row['owner_email'] . '</small></td>';
                                        echo '<td>' . $row['category_name'] . '</td>';
                                        echo '<td>' . date('Y-m-d', strtotime($row['created_at'])) . '</td>';
                                        echo '<td>
                                                <a href="store_details.php?id=' . $row['id'] . '" class="btn btn-sm btn-info"><i class="fas fa-eye"></i></a>
                                                <a href="approve_store.php?id=' . $row['id'] . '" class="btn btn-sm btn-success"><i class="fas fa-check"></i></a>
                                                <a href="reject_store.php?id=' . $row['id'] . '" class="btn btn-sm btn-danger"><i class="fas fa-times"></i></a>
                                              </td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="6" class="text-center">لا توجد طلبات جديدة</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if ($result && $result->num_rows > 0): ?>
                        <div class="text-end">
                            <a href="pending_stores.php" class="btn btn-outline-primary">عرض جميع الطلبات</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- أحدث الطلبات -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">أحدث الطلبات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>العميل</th>
                                    <th>المتجر</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $sql = "SELECT o.*, u.name as customer_name, s.name as store_name
                                        FROM orders o
                                        JOIN users u ON o.user_id = u.id
                                        JOIN stores s ON o.store_id = s.id
                                        ORDER BY o.created_at DESC
                                        LIMIT 5";
                                $result = $conn->query($sql);

                                if ($result && $result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()) {
                                        $statusClass = '';
                                        switch ($row['status']) {
                                            case 'new': $statusClass = 'bg-primary'; break;
                                            case 'processing': $statusClass = 'bg-warning text-dark'; break;
                                            case 'ready': $statusClass = 'bg-info'; break;
                                            case 'delivering': $statusClass = 'bg-info'; break;
                                            case 'completed': $statusClass = 'bg-success'; break;
                                            case 'cancelled': $statusClass = 'bg-danger'; break;
                                        }

                                        echo '<tr>';
                                        echo '<td>' . $row['id'] . '</td>';
                                        echo '<td>' . $row['customer_name'] . '</td>';
                                        echo '<td>' . $row['store_name'] . '</td>';
                                        echo '<td>' . number_format($row['total_amount'], 2) . ' ريال</td>';
                                        echo '<td><span class="badge ' . $statusClass . '">' . $row['status'] . '</span></td>';
                                        echo '<td>' . date('Y-m-d H:i', strtotime($row['created_at'])) . '</td>';
                                        echo '<td>
                                                <a href="order_details.php?id=' . $row['id'] . '" class="btn btn-sm btn-info"><i class="fas fa-eye"></i></a>
                                              </td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="7" class="text-center">لا توجد طلبات</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if ($result && $result->num_rows > 0): ?>
                        <div class="text-end">
                            <a href="orders.php" class="btn btn-outline-primary">عرض جميع الطلبات</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- رسم بياني للإحصائيات -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">إحصائيات الطلبات</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="ordersChart" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">توزيع المتاجر حسب الفئة</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="storesChart" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript لإنشاء الرسوم البيانية -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // بيانات إحصائيات الطلبات
    const ordersCtx = document.getElementById('ordersChart').getContext('2d');
    const ordersChart = new Chart(ordersCtx, {
        type: 'bar',
        data: {
            labels: ['جديد', 'قيد التجهيز', 'جاهز', 'قيد التوصيل', 'مكتمل', 'ملغي'],
            datasets: [{
                label: 'عدد الطلبات',
                data: [
                    <?php
                    $statuses = ['new', 'processing', 'ready', 'delivering', 'completed', 'cancelled'];
                    foreach ($statuses as $status) {
                        $sql = "SELECT COUNT(*) as count FROM orders WHERE status = '$status'";
                        $result = $conn->query($sql);
                        $row = $result->fetch_assoc();
                        echo $row['count'] . ', ';
                    }
                    ?>
                ],
                backgroundColor: [
                    'rgba(13, 110, 253, 0.7)',
                    'rgba(255, 193, 7, 0.7)',
                    'rgba(13, 202, 240, 0.7)',
                    'rgba(253, 126, 20, 0.7)',
                    'rgba(25, 135, 84, 0.7)',
                    'rgba(220, 53, 69, 0.7)'
                ],
                borderColor: [
                    'rgba(13, 110, 253, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(13, 202, 240, 1)',
                    'rgba(253, 126, 20, 1)',
                    'rgba(25, 135, 84, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });

    // بيانات توزيع المتاجر حسب الفئة
    const storesCtx = document.getElementById('storesChart').getContext('2d');
    const storesChart = new Chart(storesCtx, {
        type: 'pie',
        data: {
            labels: [
                <?php
                $sql = "SELECT sc.name, COUNT(s.id) as count
                        FROM store_categories sc
                        LEFT JOIN stores s ON sc.id = s.category_id
                        GROUP BY sc.id
                        ORDER BY sc.name";
                $result = $conn->query($sql);

                if ($result && $result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        echo "'" . $row['name'] . "', ";
                    }
                }
                ?>
            ],
            datasets: [{
                data: [
                    <?php
                    $result->data_seek(0); // إعادة مؤشر النتائج إلى البداية
                    if ($result && $result->num_rows > 0) {
                        while ($row = $result->fetch_assoc()) {
                            echo $row['count'] . ', ';
                        }
                    }
                    ?>
                ],
                backgroundColor: [
                    'rgba(13, 110, 253, 0.7)',
                    'rgba(25, 135, 84, 0.7)',
                    'rgba(220, 53, 69, 0.7)',
                    'rgba(255, 193, 7, 0.7)',
                    'rgba(13, 202, 240, 0.7)'
                ],
                borderColor: [
                    'rgba(13, 110, 253, 1)',
                    'rgba(25, 135, 84, 1)',
                    'rgba(220, 53, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(13, 202, 240, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
});
</script>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
