<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول ودور المستخدم
if (!isLoggedIn() || !isVendor()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كبائع للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// تضمين النماذج المطلوبة
require_once '../models/Store.php';
require_once '../models/Category.php';

// إنشاء كائنات النماذج
$storeModel = new Store($conn);
$categoryModel = new Category($conn);

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// الحصول على متجر المستخدم
$store = $storeModel->getByUserId($userId);

// إذا لم يكن للمستخدم متجر، قم بتوجيهه لإنشاء متجر
if (!$store) {
    $_SESSION['info'] = 'يجب إنشاء متجر أولاً للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/vendor/create_store.php');
}

// الحصول على معرف المتجر
$storeId = $store['id'];

// الحصول على جميع الفئات
$categories = $categoryModel->getAll();

// معالجة تحديث إعدادات المتجر
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/vendor/store_settings.php');
    }
    
    // الحصول على البيانات المرسلة
    $name = sanitize($_POST['name']);
    $description = sanitize($_POST['description']);
    $phone = sanitize($_POST['phone']);
    $address = sanitize($_POST['address']);
    $category_id = intval($_POST['category_id']);
    
    // التحقق من البيانات
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'يرجى إدخال اسم المتجر';
    }
    
    if (empty($description)) {
        $errors[] = 'يرجى إدخال وصف المتجر';
    }
    
    if (empty($phone)) {
        $errors[] = 'يرجى إدخال رقم هاتف المتجر';
    }
    
    if (empty($address)) {
        $errors[] = 'يرجى إدخال عنوان المتجر';
    }
    
    // معالجة تحميل شعار المتجر
    $logo = $store['logo']; // الاحتفاظ بالشعار الحالي
    
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2 ميجابايت
        
        if (!in_array($_FILES['logo']['type'], $allowed_types)) {
            $errors[] = 'نوع الملف غير مسموح به. يرجى تحميل صورة بصيغة JPG أو PNG أو GIF';
        }
        
        if ($_FILES['logo']['size'] > $max_size) {
            $errors[] = 'حجم الصورة كبير جداً. الحد الأقصى هو 2 ميجابايت';
        }
        
        if (empty($errors)) {
            // إنشاء اسم فريد للصورة
            $logo = 'store_logo_' . time() . '_' . $storeId . '.' . pathinfo($_FILES['logo']['name'], PATHINFO_EXTENSION);
            $upload_dir = PUBLIC_PATH . '/uploads/stores/';
            
            // التأكد من وجود المجلد
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $upload_path = $upload_dir . $logo;
            
            if (!move_uploaded_file($_FILES['logo']['tmp_name'], $upload_path)) {
                $errors[] = 'حدث خطأ أثناء تحميل الشعار';
                $logo = $store['logo']; // الاحتفاظ بالشعار الحالي في حالة فشل التحميل
            } else {
                // حذف الشعار القديم إذا لم يكن الشعار الافتراضي
                if ($store['logo'] != 'default_store.png') {
                    $old_logo_path = PUBLIC_PATH . '/uploads/stores/' . $store['logo'];
                    if (file_exists($old_logo_path)) {
                        unlink($old_logo_path);
                    }
                }
            }
        }
    }
    
    // معالجة تحميل صورة الغلاف
    $banner = $store['banner']; // الاحتفاظ بصورة الغلاف الحالية
    
    if (isset($_FILES['banner']) && $_FILES['banner']['error'] === 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2 ميجابايت
        
        if (!in_array($_FILES['banner']['type'], $allowed_types)) {
            $errors[] = 'نوع الملف غير مسموح به. يرجى تحميل صورة بصيغة JPG أو PNG أو GIF';
        }
        
        if ($_FILES['banner']['size'] > $max_size) {
            $errors[] = 'حجم الصورة كبير جداً. الحد الأقصى هو 2 ميجابايت';
        }
        
        if (empty($errors)) {
            // إنشاء اسم فريد للصورة
            $banner = 'store_banner_' . time() . '_' . $storeId . '.' . pathinfo($_FILES['banner']['name'], PATHINFO_EXTENSION);
            $upload_dir = PUBLIC_PATH . '/uploads/stores/';
            
            // التأكد من وجود المجلد
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $upload_path = $upload_dir . $banner;
            
            if (!move_uploaded_file($_FILES['banner']['tmp_name'], $upload_path)) {
                $errors[] = 'حدث خطأ أثناء تحميل صورة الغلاف';
                $banner = $store['banner']; // الاحتفاظ بصورة الغلاف الحالية في حالة فشل التحميل
            } else {
                // حذف صورة الغلاف القديمة إذا لم تكن الصورة الافتراضية
                if ($store['banner'] != 'default_banner.jpg') {
                    $old_banner_path = PUBLIC_PATH . '/uploads/stores/' . $store['banner'];
                    if (file_exists($old_banner_path)) {
                        unlink($old_banner_path);
                    }
                }
            }
        }
    }
    
    // إذا لم تكن هناك أخطاء، قم بتحديث إعدادات المتجر
    if (empty($errors)) {
        $storeModel->id = $storeId;
        $storeModel->name = $name;
        $storeModel->description = $description;
        $storeModel->logo = $logo;
        $storeModel->banner = $banner;
        $storeModel->phone = $phone;
        $storeModel->address = $address;
        $storeModel->category_id = $category_id;
        
        if ($storeModel->update()) {
            $_SESSION['success'] = 'تم تحديث إعدادات المتجر بنجاح';
            redirect(APP_URL . '/vendor/store_settings.php');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء تحديث إعدادات المتجر';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// تضمين الرأس
include_once '../includes/vendor_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إعدادات المتجر</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="<?php echo APP_URL; ?>/public/store.php?id=<?php echo $storeId; ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                        <i class="fas fa-eye"></i> عرض المتجر
                    </a>
                </div>
            </div>
            
            <!-- نموذج إعدادات المتجر -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <form action="" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم المتجر <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($store['name']); ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">وصف المتجر <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="description" name="description" rows="5" required><?php echo htmlspecialchars($store['description']); ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($store['phone']); ?>" required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="category_id" class="form-label">فئة المتجر <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">اختر الفئة</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>" <?php echo ($store['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $category['name']; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">عنوان المتجر <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($store['address']); ?></textarea>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-4">
                                    <label for="logo" class="form-label">شعار المتجر</label>
                                    <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                    <div class="form-text">الحد الأقصى للحجم: 2 ميجابايت. الصيغ المسموح بها: JPG, PNG, GIF</div>
                                    
                                    <div class="text-center mt-3">
                                        <?php
                                        $logoPath = !empty($store['logo']) ? APP_URL . '/public/uploads/stores/' . $store['logo'] : APP_URL . '/public/img/default_store.png';
                                        ?>
                                        <img src="<?php echo $logoPath; ?>" id="logo-preview" class="img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;">
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="banner" class="form-label">صورة غلاف المتجر</label>
                                    <input type="file" class="form-control" id="banner" name="banner" accept="image/*">
                                    <div class="form-text">الحد الأقصى للحجم: 2 ميجابايت. الصيغ المسموح بها: JPG, PNG, GIF</div>
                                    
                                    <div class="text-center mt-3">
                                        <?php
                                        $bannerPath = !empty($store['banner']) ? APP_URL . '/public/uploads/stores/' . $store['banner'] : APP_URL . '/public/img/default_banner.jpg';
                                        ?>
                                        <img src="<?php echo $bannerPath; ?>" id="banner-preview" class="img-thumbnail" style="width: 100%; height: 100px; object-fit: cover;">
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <p class="mb-0"><i class="fas fa-info-circle"></i> حالة المتجر: 
                                        <?php if ($store['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning text-dark">في انتظار الموافقة</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-outline-secondary">إعادة تعيين</button>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معاينة الشعار قبل التحميل
    const logoInput = document.getElementById('logo');
    const logoPreview = document.getElementById('logo-preview');
    
    if (logoInput && logoPreview) {
        logoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    logoPreview.src = e.target.result;
                }
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
    
    // معاينة صورة الغلاف قبل التحميل
    const bannerInput = document.getElementById('banner');
    const bannerPreview = document.getElementById('banner-preview');
    
    if (bannerInput && bannerPreview) {
        bannerInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    bannerPreview.src = e.target.result;
                }
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
});
</script>

<?php
// تضمين التذييل
include_once '../includes/vendor_footer.php';
?>
