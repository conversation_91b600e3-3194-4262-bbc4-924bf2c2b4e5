/* Zouk Market - New Theme Styles */

:root {
    /* Main Colors */
    --primary-color: #ff6b6b;
    --secondary-color: #4ecdc4;
    --accent-color: #ffd166;
    --dark-color: #2d3436;
    --light-color: #f8f9fa;

    /* Text Colors */
    --text-dark: #2d3436;
    --text-light: #f8f9fa;
    --text-muted: #6c757d;

    /* Status Colors */
    --success-color: #06d6a0;
    --warning-color: #ffd166;
    --danger-color: #ef476f;
    --info-color: #118ab2;

    /* Shadows */
    --card-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* General Styles */
body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--light-color);
    color: var(--text-dark);
}

/* Override Bootstrap Colors */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.bg-accent {
    background-color: var(--accent-color) !important;
}

.bg-dark {
    background-color: var(--dark-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-accent {
    color: var(--accent-color) !important;
}

/* Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #ff5252;
    border-color: #ff5252;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: #3dbeb6;
    border-color: #3dbeb6;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
}

.btn-accent {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.btn-accent:hover {
    background-color: #ffc74d;
    border-color: #ffc74d;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 209, 102, 0.4);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Navbar Styles */
.navbar {
    background-color: white !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
}

.nav-link {
    font-weight: 500;
    color: var(--text-dark) !important;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.nav-link.active {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #ff9e7d 100%);
    padding: 5rem 0;
    margin-bottom: 4rem;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/pattern.svg');
    background-size: cover;
    opacity: 0.1;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.hero-image {
    border-radius: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    transition: transform 0.5s ease;
}

.hero-image:hover {
    transform: translateY(-10px) rotate(2deg);
}

/* Card Styles */
.card {
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: var(--card-shadow);
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: var(--hover-shadow);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

.card-title {
    font-weight: 700;
    color: var(--text-dark);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: white;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
}

/* Section Styles */
.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.section-subtitle {
    color: var(--text-muted);
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Category Cards */
.category-card {
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background-color: white;
    box-shadow: var(--card-shadow);
    height: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--hover-shadow);
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    transition: transform 0.3s ease;
}

.category-card:hover .category-icon {
    transform: scale(1.2);
}

.category-title {
    font-weight: 600;
    margin-bottom: 0;
}

/* How It Works Section */
.steps-section {
    position: relative;
}

.steps-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, rgba(255,107,107,0) 0%, rgba(255,107,107,1) 50%, rgba(255,107,107,0) 100%);
    z-index: 0;
    display: none;
}

@media (min-width: 768px) {
    .steps-section::before {
        display: block;
    }
}

.step-card {
    border-radius: 20px;
    padding: 2.5rem 2rem;
    text-align: center;
    background-color: white;
    box-shadow: var(--card-shadow);
    position: relative;
    z-index: 1;
    overflow: hidden;
    height: 100%;
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.step-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.step-card:hover {
    transform: translateY(-15px);
    box-shadow: var(--hover-shadow);
}

.step-card:hover::before {
    opacity: 0.05;
}

.step-number {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    background-color: var(--accent-color);
    color: var(--text-dark);
    font-weight: 700;
    font-size: 1rem;
    z-index: 2;
}

.step-icon-wrapper {
    position: relative;
    margin-bottom: 2rem;
}

.step-icon {
    width: 90px;
    height: 90px;
    line-height: 90px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(255, 107, 107, 0.1);
    color: var(--primary-color);
    font-size: 2.2rem;
    margin: 0 auto;
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    z-index: 2;
}

.step-icon::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    opacity: 0.3;
    z-index: -1;
    transform: scale(0.8);
    transition: all 0.5s ease;
}

.step-card:hover .step-icon {
    background-color: var(--primary-color);
    color: white;
    transform: rotateY(360deg);
}

.step-card:hover .step-icon::before {
    transform: scale(1.1);
    opacity: 0.6;
}

.step-title {
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-dark);
    transition: color 0.3s ease;
    font-size: 1.4rem;
}

.step-card:hover .step-title {
    color: var(--primary-color);
}

.step-description {
    color: var(--text-muted);
    margin-bottom: 0;
    transition: color 0.3s ease;
}

.step-card:hover .step-description {
    color: var(--text-dark);
}

.step-arrow {
    position: absolute;
    top: 50%;
    right: -20px;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 2rem;
    z-index: 10;
    display: none;
}

@media (min-width: 768px) {
    .step-arrow {
        display: block;
    }
}

/* Vendor Section */
.vendor-section {
    background-color: white;
    border-radius: 15px;
    padding: 3rem;
    box-shadow: var(--card-shadow);
}

.vendor-feature {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.vendor-feature i {
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(6, 214, 160, 0.1);
    color: var(--success-color);
    margin-right: 1rem;
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: 4rem 0 2rem;
}

footer h5 {
    color: white;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

footer ul {
    padding-left: 0;
    list-style: none;
}

footer ul li {
    margin-bottom: 0.5rem;
}

footer a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: white;
    text-decoration: none;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1.5rem;
    margin-top: 3rem;
}

/* Special Offers Section */
.offer-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background-color: var(--accent-color);
    color: var(--text-dark);
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 30px;
    z-index: 2;
}

.discount-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.original-price {
    text-decoration: line-through;
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.5s ease forwards;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-section {
        padding: 3rem 0;
    }
}

@media (max-width: 768px) {
    .hero-image {
        margin-top: 2rem;
    }

    .card-img-top {
        height: 180px;
    }

    .section-title {
        font-size: 1.8rem;
    }
}
