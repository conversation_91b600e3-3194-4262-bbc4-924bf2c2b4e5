-- التحقق من وجود جدول التقييمات وإنشائه إذا لم يكن موجودًا
CREATE TABLE IF NOT EXISTS `reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `rating` int(11) NOT NULL,
  `comment` text NOT NULL,
  `reply` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- التحقق من وجود عمود product_id وإضافته إذا لم يكن موجودًا
ALTER TABLE `reviews` ADD COLUMN IF NOT EXISTS `product_id` int(11) NOT NULL AFTER `user_id`;

-- إضافة مؤشر لعمود product_id إذا لم يكن موجودًا
ALTER TABLE `reviews` ADD INDEX IF NOT EXISTS `product_id` (`product_id`);

-- إضافة عمود store_id إذا لم يكن موجودًا
ALTER TABLE `reviews` ADD COLUMN IF NOT EXISTS `store_id` int(11) NOT NULL AFTER `product_id`;

-- إضافة مؤشر لعمود store_id إذا لم يكن موجودًا
ALTER TABLE `reviews` ADD INDEX IF NOT EXISTS `store_id` (`store_id`);
