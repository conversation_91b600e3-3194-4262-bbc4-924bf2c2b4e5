<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول ودور المستخدم
if (!isLoggedIn() || !isVendor()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كبائع للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// تضمين النماذج المطلوبة
require_once '../models/Store.php';
require_once '../models/Order.php';

// إنشاء كائنات النماذج
$storeModel = new Store($conn);
$orderModel = new Order($conn);

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// الحصول على متجر المستخدم
$store = $storeModel->getByUserId($userId);

// إذا لم يكن للمستخدم متجر، قم بتوجيهه لإنشاء متجر
if (!$store) {
    $_SESSION['info'] = 'يجب إنشاء متجر أولاً للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/vendor/create_store.php');
}

// الحصول على معرف المتجر
$storeId = $store['id'];

// الحصول على حالة الطلب المحددة (إذا وجدت)
$status = isset($_GET['status']) ? $_GET['status'] : null;

// الحصول على كلمة البحث (إذا وجدت)
$searchKeyword = isset($_GET['search']) ? $_GET['search'] : '';

// الحصول على الطلبات
if (!empty($searchKeyword)) {
    // البحث عن طلب برقم معين أو اسم عميل
    $orders = $orderModel->searchByStore($storeId, $searchKeyword);
} else {
    // الحصول على الطلبات حسب الحالة
    $orders = $orderModel->getByStoreId($storeId, $status);
}

// تضمين الرأس
include_once '../includes/vendor_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة الطلبات</h1>
            </div>
            
            <!-- فلاتر البحث -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form action="" method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">بحث</label>
                            <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($searchKeyword); ?>" placeholder="ابحث برقم الطلب أو اسم العميل">
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">حالة الطلب</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الطلبات</option>
                                <option value="new" <?php echo ($status == 'new') ? 'selected' : ''; ?>>جديد</option>
                                <option value="processing" <?php echo ($status == 'processing') ? 'selected' : ''; ?>>قيد التجهيز</option>
                                <option value="ready" <?php echo ($status == 'ready') ? 'selected' : ''; ?>>جاهز للتسليم</option>
                                <option value="delivering" <?php echo ($status == 'delivering') ? 'selected' : ''; ?>>قيد التوصيل</option>
                                <option value="completed" <?php echo ($status == 'completed') ? 'selected' : ''; ?>>مكتمل</option>
                                <option value="cancelled" <?php echo ($status == 'cancelled') ? 'selected' : ''; ?>>ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">تصفية</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- أزرار تصفية سريعة -->
            <div class="mb-4">
                <div class="btn-group">
                    <a href="<?php echo APP_URL; ?>/vendor/orders.php" class="btn btn-outline-secondary <?php echo empty($status) ? 'active' : ''; ?>">
                        الكل
                    </a>
                    <a href="<?php echo APP_URL; ?>/vendor/orders.php?status=new" class="btn btn-outline-primary <?php echo ($status == 'new') ? 'active' : ''; ?>">
                        جديد
                    </a>
                    <a href="<?php echo APP_URL; ?>/vendor/orders.php?status=processing" class="btn btn-outline-warning <?php echo ($status == 'processing') ? 'active' : ''; ?>">
                        قيد التجهيز
                    </a>
                    <a href="<?php echo APP_URL; ?>/vendor/orders.php?status=ready" class="btn btn-outline-info <?php echo ($status == 'ready') ? 'active' : ''; ?>">
                        جاهز للتسليم
                    </a>
                    <a href="<?php echo APP_URL; ?>/vendor/orders.php?status=delivering" class="btn btn-outline-info <?php echo ($status == 'delivering') ? 'active' : ''; ?>">
                        قيد التوصيل
                    </a>
                    <a href="<?php echo APP_URL; ?>/vendor/orders.php?status=completed" class="btn btn-outline-success <?php echo ($status == 'completed') ? 'active' : ''; ?>">
                        مكتمل
                    </a>
                    <a href="<?php echo APP_URL; ?>/vendor/orders.php?status=cancelled" class="btn btn-outline-danger <?php echo ($status == 'cancelled') ? 'active' : ''; ?>">
                        ملغي
                    </a>
                </div>
            </div>
            
            <!-- قائمة الطلبات -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">الطلبات (<?php echo count($orders); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($orders)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد طلبات <?php echo !empty($status) ? 'بحالة "' . $status . '"' : ''; ?> حتى الآن.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>طريقة الدفع</th>
                                        <th>حالة الدفع</th>
                                        <th>حالة الطلب</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orders as $order): ?>
                                        <tr>
                                            <td>#<?php echo $order['id']; ?></td>
                                            <td><?php echo $order['user_name']; ?></td>
                                            <td><?php echo number_format($order['total_amount'], 2); ?> ريال</td>
                                            <td>
                                                <?php
                                                switch ($order['payment_method']) {
                                                    case 'cash':
                                                        echo 'نقداً عند الاستلام';
                                                        break;
                                                    case 'card':
                                                        echo 'بطاقة ائتمان';
                                                        break;
                                                    case 'bank_transfer':
                                                        echo 'تحويل بنكي';
                                                        break;
                                                    default:
                                                        echo $order['payment_method'];
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php if ($order['payment_status'] == 'paid'): ?>
                                                    <span class="badge bg-success">مدفوع</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning text-dark">غير مدفوع</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusText = '';
                                                switch ($order['status']) {
                                                    case 'new':
                                                        $statusClass = 'bg-primary';
                                                        $statusText = 'جديد';
                                                        break;
                                                    case 'processing':
                                                        $statusClass = 'bg-warning text-dark';
                                                        $statusText = 'قيد التجهيز';
                                                        break;
                                                    case 'ready':
                                                        $statusClass = 'bg-info';
                                                        $statusText = 'جاهز للتسليم';
                                                        break;
                                                    case 'delivering':
                                                        $statusClass = 'bg-info';
                                                        $statusText = 'قيد التوصيل';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'bg-success';
                                                        $statusText = 'مكتمل';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'bg-danger';
                                                        $statusText = 'ملغي';
                                                        break;
                                                }
                                                echo '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';
                                                ?>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?php echo APP_URL; ?>/vendor/order_details.php?id=<?php echo $order['id']; ?>" class="btn btn-primary">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                    <?php if ($order['status'] != 'completed' && $order['status'] != 'cancelled'): ?>
                                                        <a href="<?php echo APP_URL; ?>/vendor/update_order_status.php?id=<?php echo $order['id']; ?>" class="btn btn-success">
                                                            <i class="fas fa-edit"></i> تحديث الحالة
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/vendor_footer.php';
?>
