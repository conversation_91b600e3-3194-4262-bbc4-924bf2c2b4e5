<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelos
require_once '../models/Cart.php';
require_once '../models/Store.php';
require_once '../models/Product.php';
require_once '../models/Order.php';

// Verificar si el usuario está logueado como cliente
if(!isLoggedIn() || !isCustomer()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كعميل لإتمام عملية الشراء';
    redirect(APP_URL . '/public/login.php');
}

// Verificar ID de tienda
if(!isset($_GET['store_id']) || !is_numeric($_GET['store_id'])) {
    $_SESSION['error'] = 'معرف المتجر غير صالح';
    redirect(APP_URL . '/public/cart.php');
}

$storeId = intval($_GET['store_id']);
$userId = $_SESSION['user_id'];

// Inicializar modelos
$cartModel = new Cart($conn);
$storeModel = new Store($conn);
$productModel = new Product($conn);
$orderModel = new Order($conn);

// Obtener información de la tienda
$store = $storeModel->getById($storeId);

// Verificar si la tienda existe y está activa
if(!$store || $store['is_active'] != 1) {
    $_SESSION['error'] = 'المتجر غير موجود أو غير متاح';
    redirect(APP_URL . '/public/cart.php');
}

// Obtener elementos del carrito para esta tienda
$cartItems = $cartModel->getCartItemsByStore($userId, $storeId);

// Verificar si hay elementos en el carrito para esta tienda
if(empty($cartItems)) {
    $_SESSION['error'] = 'لا توجد منتجات في سلة التسوق من هذا المتجر';
    redirect(APP_URL . '/public/cart.php');
}

// Calcular el total del pedido
$orderTotal = 0;
foreach($cartItems as $item) {
    $orderTotal += $item['price'] * $item['quantity'];
}

// Procesar el formulario de pedido
if($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar token CSRF
    if(!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/public/checkout.php?store_id=' . $storeId);
    }
    
    // Obtener datos del formulario
    $address = sanitize($_POST['address']);
    $phone = sanitize($_POST['phone']);
    $notes = sanitize($_POST['notes']);
    $paymentMethod = sanitize($_POST['payment_method']);
    
    // Validar datos
    $errors = [];
    
    if(empty($address)) {
        $errors[] = 'يرجى إدخال العنوان';
    }
    
    if(empty($phone)) {
        $errors[] = 'يرجى إدخال رقم الهاتف';
    }
    
    if(empty($paymentMethod)) {
        $errors[] = 'يرجى اختيار طريقة الدفع';
    }
    
    // Si no hay errores, crear el pedido
    if(empty($errors)) {
        // Verificar stock de productos
        $stockError = false;
        foreach($cartItems as $item) {
            $product = $productModel->getById($item['product_id']);
            if($product['stock_quantity'] < $item['quantity']) {
                $stockError = true;
                $errors[] = 'المنتج ' . $product['name'] . ' غير متوفر بالكمية المطلوبة';
            }
        }
        
        if(!$stockError) {
            // Crear el pedido
            $orderModel->user_id = $userId;
            $orderModel->store_id = $storeId;
            $orderModel->total_amount = $orderTotal;
            $orderModel->status = 'new';
            $orderModel->payment_method = $paymentMethod;
            $orderModel->payment_status = $paymentMethod === 'cash' ? 'pending' : 'paid';
            $orderModel->address = $address;
            $orderModel->phone = $phone;
            $orderModel->notes = $notes;
            
            if($orderModel->create()) {
                $orderId = $orderModel->id;
                
                // Agregar elementos del pedido
                foreach($cartItems as $item) {
                    $orderModel->addOrderItem($orderId, $item['product_id'], $item['quantity'], $item['price']);
                    
                    // Actualizar stock del producto
                    $productModel->updateStock($item['product_id'], $item['quantity']);
                }
                
                // Vaciar el carrito para esta tienda
                $cartModel->clearCartByStore($userId, $storeId);
                
                // Crear notificación para el vendedor
                $sql = "INSERT INTO notifications (user_id, title, message, is_read, created_at)
                        VALUES (?, 'طلب جديد', 'لديك طلب جديد برقم #" . $orderId . "', 0, NOW())";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $store['user_id']);
                $stmt->execute();
                
                // Redirigir a la página de confirmación
                $_SESSION['success'] = 'تم إنشاء الطلب بنجاح';
                redirect(APP_URL . '/customer/order_details.php?id=' . $orderId);
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء إنشاء الطلب';
            }
        }
    }
    
    if(!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">إتمام الطلب</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <h5 class="mb-3">معلومات التوصيل</h5>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="3" required><?php echo isset($_POST['address']) ? $_POST['address'] : ''; ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? $_POST['phone'] : ''; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2"><?php echo isset($_POST['notes']) ? $_POST['notes'] : ''; ?></textarea>
                        </div>
                        
                        <h5 class="mb-3 mt-4">طريقة الدفع</h5>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_cash" value="cash" <?php echo (!isset($_POST['payment_method']) || $_POST['payment_method'] === 'cash') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="payment_cash">
                                    <i class="fas fa-money-bill-wave me-2"></i> الدفع عند الاستلام
                                </label>
                            </div>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_card" value="card" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] === 'card') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="payment_card">
                                    <i class="fas fa-credit-card me-2"></i> بطاقة ائتمان
                                </label>
                            </div>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="radio" name="payment_method" id="payment_bank" value="bank_transfer" <?php echo (isset($_POST['payment_method']) && $_POST['payment_method'] === 'bank_transfer') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="payment_bank">
                                    <i class="fas fa-university me-2"></i> تحويل بنكي
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">تأكيد الطلب</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">ملخص الطلب</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <?php
                        // التحقق من وجود شعار المتجر
                        $storeLogoPath = ROOT_PATH . '/public/uploads/stores/' . $store['logo'];
                        $storeLogoUrl = APP_URL . '/public/uploads/stores/' . $store['logo'];
                        
                        if (!file_exists($storeLogoPath) || empty($store['logo'])) {
                            // إذا لم يكن الشعار موجوداً، استخدم الشعار الافتراضي
                            $defaultStoreLogoPath = ROOT_PATH . '/public/img/default_store.png';
                            $storeLogoUrl = APP_URL . '/public/img/default_store.png';
                            
                            if (!file_exists($defaultStoreLogoPath)) {
                                // إذا لم يكن الشعار الافتراضي موجوداً، استخدم صورة من الإنترنت
                                $storeLogoUrl = 'https://via.placeholder.com/50x50?text=S';
                            }
                        }
                        ?>
                        <img src="<?php echo $storeLogoUrl; ?>" alt="<?php echo $store['name']; ?>" class="me-2 rounded-circle" width="40" height="40">
                        <h5 class="mb-0"><?php echo $store['name']; ?></h5>
                    </div>
                    
                    <hr>
                    
                    <h6 class="mb-3">المنتجات</h6>
                    
                    <?php foreach($cartItems as $item): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <div>
                                <span><?php echo $item['name']; ?></span>
                                <small class="d-block text-muted"><?php echo $item['quantity']; ?> × <?php echo number_format($item['price'], 2); ?> ريال</small>
                            </div>
                            <span><?php echo number_format($item['price'] * $item['quantity'], 2); ?> ريال</span>
                        </div>
                    <?php endforeach; ?>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>المجموع الفرعي:</span>
                        <span><?php echo number_format($orderTotal, 2); ?> ريال</span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>رسوم التوصيل:</span>
                        <span>0.00 ريال</span>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <strong>الإجمالي:</strong>
                        <strong><?php echo number_format($orderTotal, 2); ?> ريال</strong>
                    </div>
                </div>
            </div>
            
            <div class="card shadow-sm">
                <div class="card-body">
                    <a href="<?php echo APP_URL; ?>/public/cart.php" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-arrow-left me-2"></i> العودة إلى السلة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
