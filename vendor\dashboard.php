<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول ودور المستخدم
if (!isLoggedIn() || !isVendor()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كبائع للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المتجر
require_once '../models/Store.php';
require_once '../models/Product.php';
require_once '../models/Order.php';

// إنشاء كائنات النماذج
$storeModel = new Store($conn);
$productModel = new Product($conn);
$orderModel = new Order($conn);

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// الحصول على متجر المستخدم
$store = $storeModel->getByUserId($userId);

// إذا لم يكن للمستخدم متجر، قم بتوجيهه لإنشاء متجر
if (!$store) {
    $_SESSION['info'] = 'يجب إنشاء متجر أولاً للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/vendor/create_store.php');
}

// الحصول على إحصائيات المتجر
$storeId = $store['id'];

// عدد المنتجات
$sql = "SELECT COUNT(*) as count FROM products WHERE store_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $storeId);
$stmt->execute();
$result = $stmt->get_result();
$productCount = $result->fetch_assoc()['count'];

// عدد الطلبات
$sql = "SELECT COUNT(*) as count FROM orders WHERE store_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $storeId);
$stmt->execute();
$result = $stmt->get_result();
$orderCount = $result->fetch_assoc()['count'];

// إجمالي المبيعات
$sql = "SELECT SUM(total_amount) as total FROM orders WHERE store_id = ? AND status = 'completed'";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $storeId);
$stmt->execute();
$result = $stmt->get_result();
$totalSales = $result->fetch_assoc()['total'] ?? 0;

// الحصول على آخر الطلبات
$sql = "SELECT o.*, u.name as customer_name 
        FROM orders o 
        JOIN users u ON o.user_id = u.id 
        WHERE o.store_id = ? 
        ORDER BY o.created_at DESC 
        LIMIT 5";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $storeId);
$stmt->execute();
$result = $stmt->get_result();
$recentOrders = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $recentOrders[] = $row;
    }
}

// تضمين الرأس
include_once '../includes/vendor_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">لوحة تحكم المتجر</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="<?php echo APP_URL; ?>/vendor/store_settings.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-cog"></i> إعدادات المتجر
                        </a>
                        <a href="<?php echo APP_URL; ?>/public/store.php?id=<?php echo $storeId; ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="fas fa-eye"></i> عرض المتجر
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- ترحيب -->
            <div class="alert alert-info">
                <h4 class="alert-heading">مرحباً <?php echo $_SESSION['user_name']; ?>!</h4>
                <p>مرحباً بك في لوحة تحكم متجرك "<?php echo $store['name']; ?>". من هنا يمكنك إدارة منتجاتك وطلباتك ومتابعة أداء متجرك.</p>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card bg-primary text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">إجمالي المنتجات</h6>
                                    <h2 class="mb-0"><?php echo $productCount; ?></h2>
                                </div>
                                <i class="fas fa-box-open fa-2x opacity-50"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a href="<?php echo APP_URL; ?>/vendor/products.php" class="text-white stretched-link">عرض التفاصيل</a>
                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card bg-success text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">إجمالي الطلبات</h6>
                                    <h2 class="mb-0"><?php echo $orderCount; ?></h2>
                                </div>
                                <i class="fas fa-shopping-cart fa-2x opacity-50"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a href="<?php echo APP_URL; ?>/vendor/orders.php" class="text-white stretched-link">عرض التفاصيل</a>
                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card bg-info text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">إجمالي المبيعات</h6>
                                    <h2 class="mb-0"><?php echo number_format($totalSales, 2); ?> شيكل</h2>
                                </div>
                                <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between">
                            <a href="<?php echo APP_URL; ?>/vendor/reports.php" class="text-white stretched-link">عرض التقارير</a>
                            <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- آخر الطلبات -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">آخر الطلبات</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recentOrders)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد طلبات حتى الآن.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>حالة الطلب</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentOrders as $order): ?>
                                        <tr>
                                            <td>#<?php echo $order['id']; ?></td>
                                            <td><?php echo $order['customer_name']; ?></td>
                                            <td><?php echo number_format($order['total_amount'], 2); ?> شيكل</td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusText = '';
                                                switch ($order['status']) {
                                                    case 'new':
                                                        $statusClass = 'bg-primary';
                                                        $statusText = 'جديد';
                                                        break;
                                                    case 'processing':
                                                        $statusClass = 'bg-warning text-dark';
                                                        $statusText = 'قيد التجهيز';
                                                        break;
                                                    case 'ready':
                                                        $statusClass = 'bg-info';
                                                        $statusText = 'جاهز';
                                                        break;
                                                    case 'delivering':
                                                        $statusClass = 'bg-info';
                                                        $statusText = 'قيد التوصيل';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'bg-success';
                                                        $statusText = 'مكتمل';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'bg-danger';
                                                        $statusText = 'ملغي';
                                                        break;
                                                }
                                                echo '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';
                                                ?>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></td>
                                            <td>
                                                <a href="<?php echo APP_URL; ?>/vendor/order_details.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="<?php echo APP_URL; ?>/vendor/orders.php" class="btn btn-primary">عرض جميع الطلبات</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- روابط سريعة -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">إضافة منتج جديد</h5>
                            <p class="card-text">قم بإضافة منتجات جديدة إلى متجرك لزيادة المبيعات.</p>
                            <a href="<?php echo APP_URL; ?>/vendor/add_product.php" class="btn btn-primary">إضافة منتج</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-3x text-success mb-3"></i>
                            <h5 class="card-title">تقارير المبيعات</h5>
                            <p class="card-text">اطلع على تقارير مفصلة عن مبيعات متجرك وأداء المنتجات.</p>
                            <a href="<?php echo APP_URL; ?>/vendor/reports.php" class="btn btn-success">عرض التقارير</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-cog fa-3x text-info mb-3"></i>
                            <h5 class="card-title">إعدادات المتجر</h5>
                            <p class="card-text">قم بتحديث معلومات متجرك وإعدادات العرض والتوصيل.</p>
                            <a href="<?php echo APP_URL; ?>/vendor/store_settings.php" class="btn btn-info">تعديل الإعدادات</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/vendor_footer.php';
?>
