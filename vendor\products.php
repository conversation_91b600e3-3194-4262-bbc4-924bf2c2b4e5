<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول ودور المستخدم
if (!isLoggedIn() || !isVendor()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كبائع للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// تضمين النماذج المطلوبة
require_once '../models/Store.php';
require_once '../models/Product.php';
require_once '../models/Category.php';

// إنشاء كائنات النماذج
$storeModel = new Store($conn);
$productModel = new Product($conn);
$categoryModel = new Category($conn);

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// الحصول على متجر المستخدم
$store = $storeModel->getByUserId($userId);

// إذا لم يكن للمستخدم متجر، قم بتوجيهه لإنشاء متجر
if (!$store) {
    $_SESSION['info'] = 'يجب إنشاء متجر أولاً للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/vendor/create_store.php');
}

// الحصول على معرف المتجر
$storeId = $store['id'];

// معالجة حذف المنتج
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $productId = intval($_GET['delete']);

    // التحقق من أن المنتج ينتمي إلى المتجر
    $product = $productModel->getById($productId);

    if ($product && $product['store_id'] == $storeId) {
        if ($productModel->delete($productId, $storeId)) {
            $_SESSION['success'] = 'تم حذف المنتج بنجاح';
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء حذف المنتج';
        }
    } else {
        $_SESSION['error'] = 'لا يمكنك حذف هذا المنتج';
    }

    redirect(APP_URL . '/vendor/products.php');
}

// الحصول على الفئة المحددة (إذا وجدت)
$categoryId = isset($_GET['category']) ? intval($_GET['category']) : null;

// الحصول على كلمة البحث (إذا وجدت)
$searchKeyword = isset($_GET['search']) ? $_GET['search'] : '';

// الحصول على المنتجات
if (!empty($searchKeyword)) {
    $products = $productModel->search($searchKeyword, $storeId);
} elseif ($categoryId) {
    $products = $productModel->getByStoreId($storeId, $categoryId);
} else {
    $products = $productModel->getByStoreId($storeId);
}

// الحصول على جميع الفئات
$categories = $categoryModel->getAll();

// تضمين الرأس
include_once '../includes/vendor_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المنتجات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="<?php echo APP_URL; ?>/vendor/add_product.php" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </a>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form action="" method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">بحث</label>
                            <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($searchKeyword); ?>" placeholder="ابحث عن اسم المنتج أو الوصف">
                        </div>
                        <div class="col-md-4">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">جميع الفئات</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo ($categoryId == $category['id']) ? 'selected' : ''; ?>>
                                        <?php echo $category['name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">تصفية</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة المنتجات -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">المنتجات (<?php echo count($products); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($products)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد منتجات حتى الآن. <a href="<?php echo APP_URL; ?>/vendor/add_product.php">أضف منتجًا جديدًا</a>.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="80">الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>الفئة</th>
                                        <th>السعر</th>
                                        <th>المخزون</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td>
                                                <?php
                                                $imagePath = !empty($product['image']) ? APP_URL . '/public/uploads/products/' . $product['image'] : APP_URL . '/public/img/default_product.png';
                                                ?>
                                                <img src="<?php echo $imagePath; ?>" alt="<?php echo $product['name']; ?>" class="img-thumbnail" width="60">
                                            </td>
                                            <td><?php echo $product['name']; ?></td>
                                            <td><?php echo $product['category_name'] ?? 'غير مصنف'; ?></td>
                                            <td><?php echo number_format($product['price'], 2); ?> شيكل</td>
                                            <td>
                                                <?php if ($product['stock_quantity'] > 10): ?>
                                                    <span class="badge bg-success"><?php echo $product['stock_quantity']; ?></span>
                                                <?php elseif ($product['stock_quantity'] > 0): ?>
                                                    <span class="badge bg-warning text-dark"><?php echo $product['stock_quantity']; ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">نفذ المخزون</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($product['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?php echo APP_URL; ?>/vendor/edit_product.php?id=<?php echo $product['id']; ?>" class="btn btn-primary">
                                                        <i class="fas fa-edit"></i> تعديل
                                                    </a>
                                                    <a href="<?php echo APP_URL; ?>/public/product.php?id=<?php echo $product['id']; ?>" class="btn btn-info" target="_blank">
                                                        <i class="fas fa-eye"></i> عرض
                                                    </a>
                                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $product['id']; ?>">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </button>
                                                </div>

                                                <!-- Modal تأكيد الحذف -->
                                                <div class="modal fade" id="deleteModal<?php echo $product['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $product['id']; ?>" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="deleteModalLabel<?php echo $product['id']; ?>">تأكيد الحذف</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                هل أنت متأكد من رغبتك في حذف المنتج "<?php echo $product['name']; ?>"؟
                                                                <br>
                                                                <strong class="text-danger">تحذير: هذا الإجراء لا يمكن التراجع عنه.</strong>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                                <a href="<?php echo APP_URL; ?>/vendor/products.php?delete=<?php echo $product['id']; ?>" class="btn btn-danger">حذف</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/vendor_footer.php';
?>
