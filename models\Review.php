<?php
/**
 * Review Model
 */
class Review {
    // Database connection
    private $conn;

    // Table name
    private $table = 'reviews';

    // Properties
    public $id;
    public $user_id;
    public $product_id;
    public $rating;
    public $comment;
    public $reply;

    /**
     * Constructor
     *
     * @param object $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Get all reviews
     *
     * @param int $limit Optional limit
     * @return array
     */
    public function getAll($limit = null) {
        // Create query
        $query = "SELECT r.*, u.name as user_name, p.name as product_name, p.image as product_image, s.id as store_id
                  FROM " . $this->table . " r
                  JOIN users u ON r.user_id = u.id
                  JOIN products p ON r.product_id = p.id
                  JOIN stores s ON p.store_id = s.id
                  ORDER BY r.created_at DESC";

        // Add limit if provided
        if($limit) {
            $query .= " LIMIT " . intval($limit);
        }

        // Execute query
        $result = $this->conn->query($query);

        $reviews = [];

        while($row = $result->fetch_assoc()) {
            $reviews[] = $row;
        }

        return $reviews;
    }

    /**
     * Get review by ID
     *
     * @param int $id
     * @return array|boolean
     */
    public function getById($id) {
        // Create query
        $query = "SELECT r.*, u.name as user_name, p.name as product_name, p.image as product_image, s.id as store_id
                  FROM " . $this->table . " r
                  JOIN users u ON r.user_id = u.id
                  JOIN products p ON r.product_id = p.id
                  JOIN stores s ON p.store_id = s.id
                  WHERE r.id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind ID
        $stmt->bind_param("i", $id);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        if($result->num_rows > 0) {
            return $result->fetch_assoc();
        } else {
            return false;
        }
    }

    /**
     * Get reviews by product ID
     *
     * @param int $productId
     * @param int $limit Optional limit
     * @return array
     */
    public function getByProductId($productId, $limit = null) {
        try {
            // Verificar si existe la columna product_id
            $checkQuery = "SHOW COLUMNS FROM " . $this->table . " LIKE 'product_id'";
            $result = $this->conn->query($checkQuery);

            if ($result->num_rows == 0) {
                // La columna product_id no existe
                return [];
            }

            // Create query
            $query = "SELECT r.*, u.name as user_name, u.profile_image as user_image
                      FROM " . $this->table . " r
                      JOIN users u ON r.user_id = u.id
                      WHERE r.product_id = ?
                      ORDER BY r.created_at DESC";

            // Add limit if provided
            if($limit) {
                $query .= " LIMIT " . intval($limit);
            }

            // Prepare statement
            $stmt = $this->conn->prepare($query);

            // Bind product ID
            $stmt->bind_param("i", $productId);

            // Execute query
            $stmt->execute();

            // Get result
            $result = $stmt->get_result();

            $reviews = [];

            while($row = $result->fetch_assoc()) {
                $reviews[] = $row;
            }

            return $reviews;
        } catch (Exception $e) {
            // En caso de error, devolver un array vacío
            return [];
        }
    }

    /**
     * Get reviews by store ID
     *
     * @param int $storeId
     * @param int $limit Optional limit
     * @return array
     */
    public function getByStoreId($storeId, $limit = null) {
        try {
            // Verificar si existe la columna product_id
            $checkQuery = "SHOW COLUMNS FROM " . $this->table . " LIKE 'product_id'";
            $result = $this->conn->query($checkQuery);

            if ($result->num_rows == 0) {
                // La columna product_id no existe
                return [];
            }

            // Create query
            $query = "SELECT r.*, u.name as user_name, u.profile_image as user_image, p.name as product_name, p.image as product_image, p.id as product_id
                      FROM " . $this->table . " r
                      JOIN users u ON r.user_id = u.id
                      JOIN products p ON r.product_id = p.id
                      WHERE p.store_id = ?
                      ORDER BY r.created_at DESC";

            // Add limit if provided
            if($limit) {
                $query .= " LIMIT " . intval($limit);
            }

            // Prepare statement
            $stmt = $this->conn->prepare($query);

            // Bind store ID
            $stmt->bind_param("i", $storeId);

            // Execute query
            $stmt->execute();

            // Get result
            $result = $stmt->get_result();

            $reviews = [];

            while($row = $result->fetch_assoc()) {
                $reviews[] = $row;
            }

            return $reviews;
        } catch (Exception $e) {
            // En caso de error, devolver un array vacío
            return [];
        }
    }

    /**
     * Get reviews by user ID
     *
     * @param int $userId
     * @param int $limit Optional limit
     * @return array
     */
    public function getByUserId($userId, $limit = null) {
        try {
            // Verificar si existe la columna product_id
            $checkQuery = "SHOW COLUMNS FROM " . $this->table . " LIKE 'product_id'";
            $result = $this->conn->query($checkQuery);

            if ($result->num_rows == 0) {
                // La columna product_id no existe
                return [];
            }

            // Create query
            $query = "SELECT r.*, p.name as product_name, p.image as product_image, p.id as product_id, s.name as store_name, s.id as store_id
                      FROM " . $this->table . " r
                      JOIN products p ON r.product_id = p.id
                      JOIN stores s ON p.store_id = s.id
                      WHERE r.user_id = ?
                      ORDER BY r.created_at DESC";

            // Add limit if provided
            if($limit) {
                $query .= " LIMIT " . intval($limit);
            }

            // Prepare statement
            $stmt = $this->conn->prepare($query);

            // Bind user ID
            $stmt->bind_param("i", $userId);

            // Execute query
            $stmt->execute();

            // Get result
            $result = $stmt->get_result();

            $reviews = [];

            while($row = $result->fetch_assoc()) {
                $reviews[] = $row;
            }

            return $reviews;
        } catch (Exception $e) {
            // En caso de error, devolver un array vacío
            return [];
        }
    }

    /**
     * Create review
     *
     * @return boolean
     */
    public function create() {
        try {
            // Verificar si existe la columna product_id
            $checkQuery = "SHOW COLUMNS FROM " . $this->table . " LIKE 'product_id'";
            $result = $this->conn->query($checkQuery);

            if ($result->num_rows == 0) {
                // La columna product_id no existe, intentar crear la tabla
                $createTableQuery = file_get_contents(ROOT_PATH . '/database/create_reviews_table.sql');
                $this->conn->multi_query($createTableQuery);

                // Limpiar resultados pendientes
                while ($this->conn->more_results() && $this->conn->next_result()) {
                    if ($result = $this->conn->store_result()) {
                        $result->free();
                    }
                }
            }

            // Create query
            $query = "INSERT INTO " . $this->table . "
                      (user_id, product_id, rating, comment, created_at, updated_at)
                      VALUES (?, ?, ?, ?, NOW(), NOW())";

            // Prepare statement
            $stmt = $this->conn->prepare($query);

            // Clean data
            $this->comment = htmlspecialchars(strip_tags($this->comment));

            // Bind data
            $stmt->bind_param("iiis",
                $this->user_id,
                $this->product_id,
                $this->rating,
                $this->comment
            );

            // Execute query
            if($stmt->execute()) {
                $this->id = $this->conn->insert_id;
                return true;
            }

            // Print error if something goes wrong
            printf("Error: %s.\n", $stmt->error);

            return false;
        } catch (Exception $e) {
            // Print error if something goes wrong
            printf("Error: %s.\n", $e->getMessage());
            return false;
        }
    }

    /**
     * Update review
     *
     * @return boolean
     */
    public function update() {
        // Create query
        $query = "UPDATE " . $this->table . "
                  SET rating = ?, comment = ?, updated_at = NOW()
                  WHERE id = ? AND user_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $this->comment = htmlspecialchars(strip_tags($this->comment));

        // Bind data
        $stmt->bind_param("isii",
            $this->rating,
            $this->comment,
            $this->id,
            $this->user_id
        );

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Add reply to review
     *
     * @param int $id Review ID
     * @param string $reply Reply text
     * @return boolean
     */
    public function addReply($id, $reply) {
        // Create query
        $query = "UPDATE " . $this->table . "
                  SET reply = ?, updated_at = NOW()
                  WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Clean data
        $reply = htmlspecialchars(strip_tags($reply));

        // Bind data
        $stmt->bind_param("si", $reply, $id);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Delete review
     *
     * @param int $id
     * @return boolean
     */
    public function delete($id) {
        // Create query
        $query = "DELETE FROM " . $this->table . " WHERE id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind ID
        $stmt->bind_param("i", $id);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Get average rating for product
     *
     * @param int $productId
     * @return float
     */
    public function getAverageRating($productId) {
        try {
            // Verificar si existe la columna product_id
            $checkQuery = "SHOW COLUMNS FROM " . $this->table . " LIKE 'product_id'";
            $result = $this->conn->query($checkQuery);

            if ($result->num_rows == 0) {
                // La columna product_id no existe
                return 0;
            }

            // Create query
            $query = "SELECT AVG(rating) as avg_rating FROM " . $this->table . " WHERE product_id = ?";

            // Prepare statement
            $stmt = $this->conn->prepare($query);

            // Bind product ID
            $stmt->bind_param("i", $productId);

            // Execute query
            $stmt->execute();

            // Get result
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();

            return $row['avg_rating'] ? round($row['avg_rating'], 1) : 0;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Get average rating for store
     *
     * @param int $storeId
     * @return float
     */
    public function getAverageRatingForStore($storeId) {
        try {
            // Verificar si existe la columna product_id
            $checkQuery = "SHOW COLUMNS FROM " . $this->table . " LIKE 'product_id'";
            $result = $this->conn->query($checkQuery);

            if ($result->num_rows == 0) {
                // La columna product_id no existe
                return 0;
            }

            // Create query
            $query = "SELECT AVG(r.rating) as avg_rating
                      FROM " . $this->table . " r
                      JOIN products p ON r.product_id = p.id
                      WHERE p.store_id = ?";

            // Prepare statement
            $stmt = $this->conn->prepare($query);

            // Bind store ID
            $stmt->bind_param("i", $storeId);

            // Execute query
            $stmt->execute();

            // Get result
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();

            return $row['avg_rating'] ? round($row['avg_rating'], 1) : 0;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Check if user has already reviewed a product
     *
     * @param int $userId
     * @param int $productId
     * @return boolean
     */
    public function hasUserReviewedProduct($userId, $productId) {
        try {
            // Verificar si existe la columna product_id
            $checkQuery = "SHOW COLUMNS FROM " . $this->table . " LIKE 'product_id'";
            $result = $this->conn->query($checkQuery);

            if ($result->num_rows == 0) {
                // La columna product_id no existe
                return false;
            }

            // Create query
            $query = "SELECT COUNT(*) as count FROM " . $this->table . "
                      WHERE user_id = ? AND product_id = ?";

            // Prepare statement
            $stmt = $this->conn->prepare($query);

            // Bind parameters
            $stmt->bind_param("ii", $userId, $productId);

            // Execute query
            $stmt->execute();

            // Get result
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();

            return $row['count'] > 0;
        } catch (Exception $e) {
            return false;
        }
    }
}
?>
