<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelos
require_once '../models/Store.php';
require_once '../models/Product.php';
require_once '../models/StoreReview.php';

// Verificar ID de tienda
if(!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'ID de tienda no válido';
    redirect(APP_URL . '/public/stores.php');
}

$storeId = intval($_GET['id']);

// Inicializar modelos
$storeModel = new Store($conn);
$productModel = new Product($conn);
$storeReviewModel = new StoreReview($conn);

// Obtener información de la tienda
$store = $storeModel->getById($storeId);

// Verificar si la tienda existe y está activa
if(!$store || $store['is_active'] != 1) {
    $_SESSION['error'] = 'La tienda no existe o no está disponible';
    redirect(APP_URL . '/public/stores.php');
}

// Obtener categoría de producto seleccionada (si existe)
$categoryId = isset($_GET['category']) ? intval($_GET['category']) : null;

// Obtener término de búsqueda (si existe)
$searchTerm = isset($_GET['search']) ? sanitize($_GET['search']) : null;

// Obtener productos
if($searchTerm) {
    // Buscar productos por término
    $products = $productModel->search($searchTerm, $storeId);
    $pageTitle = 'نتائج البحث: ' . $searchTerm;
} else {
    // Obtener productos por categoría o todos
    $products = $productModel->getByStoreId($storeId, $categoryId);
    $pageTitle = $categoryId ? 'المنتجات في الفئة' : 'جميع المنتجات';
}

// Obtener reseñas de la tienda
$storeReviews = $storeReviewModel->getByStoreId($storeId);

// Calcular promedio de calificaciones
$averageRating = $storeReviewModel->getAverageRating($storeId);

// Obtener distribución de calificaciones
$ratingDistribution = $storeReviewModel->getRatingDistribution($storeId);
$ratingCount = count($storeReviews);

// Verificar si el usuario ya ha calificado esta tienda
$userHasReviewed = false;
if (isLoggedIn()) {
    $userHasReviewed = $storeReviewModel->hasUserReviewedStore($_SESSION['user_id'], $storeId);
}

// Procesar envío de reseña
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_review'])) {
    // Verificar si el usuario está logueado
    if (!isLoggedIn()) {
        $_SESSION['error'] = 'يجب تسجيل الدخول لإضافة تقييم';
        redirect(APP_URL . '/public/login.php');
    }

    // Verificar token CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/public/store.php?id=' . $storeId);
    }

    // Verificar si el usuario ya ha calificado esta tienda
    if ($userHasReviewed) {
        $_SESSION['error'] = 'لقد قمت بتقييم هذا المتجر مسبقاً';
        redirect(APP_URL . '/public/store.php?id=' . $storeId);
    }

    // Obtener datos del formulario
    $rating = intval($_POST['rating']);
    $comment = sanitize($_POST['comment']);

    // Validar datos
    $errors = [];

    if ($rating < 1 || $rating > 5) {
        $errors[] = 'التقييم يجب أن يكون بين 1 و 5';
    }

    if (empty($comment)) {
        $errors[] = 'يرجى إدخال تعليق';
    }

    // Si no hay errores, guardar la reseña
    if (empty($errors)) {
        $storeReviewModel->user_id = $_SESSION['user_id'];
        $storeReviewModel->store_id = $storeId;
        $storeReviewModel->rating = $rating;
        $storeReviewModel->comment = $comment;

        if ($storeReviewModel->create()) {
            $_SESSION['success'] = 'تم إضافة التقييم بنجاح';
            redirect(APP_URL . '/public/store.php?id=' . $storeId);
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء إضافة التقييم';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }

    redirect(APP_URL . '/public/store.php?id=' . $storeId);
}

// Obtener categorías de productos de la tienda
$sql = "SELECT * FROM product_categories WHERE store_id = ? ORDER BY name";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $storeId);
$stmt->execute();
$result = $stmt->get_result();
$categories = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
}

// Incluir el encabezado
include_once '../includes/header.php';
?>

<!-- Banner de la tienda -->
<div class="card mb-4 shadow-sm">
    <div class="card-body p-0">
        <?php
        // التحقق من وجود صورة الغلاف
        $hasBanner = false;
        if (!empty($store['banner'])) {
            $bannerPath = ROOT_PATH . '/public/uploads/stores/' . $store['banner'];
            if (file_exists($bannerPath)) {
                $hasBanner = true;
                $bannerUrl = APP_URL . '/public/uploads/stores/' . $store['banner'];
            }
        }

        if ($hasBanner): ?>
            <img src="<?php echo $bannerUrl; ?>" class="w-100 store-banner" alt="<?php echo $store['name']; ?>">
        <?php else: ?>
            <div class="bg-light p-5 text-center">
                <h3><?php echo $store['name']; ?></h3>
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <!-- Información de la tienda -->
    <div class="col-md-3 mb-4">
        <div class="card shadow-sm">
            <div class="card-body text-center">
                <?php
                // التحقق من وجود شعار المتجر
                $storeLogoPath = ROOT_PATH . '/public/uploads/stores/' . $store['logo'];
                $storeLogoUrl = APP_URL . '/public/uploads/stores/' . $store['logo'];

                if (!file_exists($storeLogoPath) || empty($store['logo'])) {
                    // إذا لم يكن الشعار موجوداً، استخدم الشعار الافتراضي
                    $defaultStoreLogoPath = ROOT_PATH . '/public/img/default_store.png';
                    $storeLogoUrl = APP_URL . '/public/img/default_store.png';

                    if (!file_exists($defaultStoreLogoPath)) {
                        // إذا لم يكن الشعار الافتراضي موجوداً، استخدم صورة من الإنترنت
                        $storeLogoUrl = 'https://via.placeholder.com/150x150?text=Store';
                    }
                }
                ?>
                <img src="<?php echo $storeLogoUrl; ?>" class="store-logo mb-3" alt="<?php echo $store['name']; ?>">
                <h4 class="card-title"><?php echo $store['name']; ?></h4>
                <?php if (isset($store['category_name'])): ?>
                    <p class="card-text text-muted"><?php echo $store['category_name']; ?></p>
                <?php else: ?>
                    <?php
                    // الحصول على اسم الفئة من جدول الفئات
                    $categoryName = '';
                    if (isset($store['category_id'])) {
                        $sql = "SELECT name FROM categories WHERE id = ?";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param("i", $store['category_id']);
                        $stmt->execute();
                        $result = $stmt->get_result();
                        if ($result && $result->num_rows > 0) {
                            $categoryName = $result->fetch_assoc()['name'];
                        }
                    }
                    ?>
                    <p class="card-text text-muted"><?php echo $categoryName; ?></p>
                <?php endif; ?>
                <hr>
                <p class="card-text"><?php echo $store['description']; ?></p>
                <hr>
                <p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i> <?php echo $store['address']; ?></p>
                <p class="mb-0"><i class="fas fa-phone me-2"></i> <?php echo $store['phone']; ?></p>
            </div>
        </div>

        <!-- Categorías de productos -->
        <?php if(!empty($categories)): ?>
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">الفئات</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="<?php echo APP_URL; ?>/public/store.php?id=<?php echo $storeId; ?>" class="list-group-item list-group-item-action <?php echo !$categoryId ? 'active' : ''; ?>">
                            جميع المنتجات
                        </a>
                        <?php foreach($categories as $category): ?>
                            <a href="<?php echo APP_URL; ?>/public/store.php?id=<?php echo $storeId; ?>&category=<?php echo $category['id']; ?>" class="list-group-item list-group-item-action <?php echo $categoryId == $category['id'] ? 'active' : ''; ?>">
                                <?php echo $category['name']; ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Formulario de búsqueda -->
        <div class="card shadow-sm mt-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">بحث</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo APP_URL; ?>/public/store.php" method="GET">
                    <input type="hidden" name="id" value="<?php echo $storeId; ?>">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" placeholder="ابحث عن منتج..." value="<?php echo $searchTerm ?? ''; ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Lista de productos -->
    <div class="col-md-9">
        <h2 class="mb-4">منتجات <?php echo $store['name']; ?></h2>

        <?php if(empty($products)): ?>
            <div class="alert alert-info">
                <p class="mb-0">لم يتم العثور على منتجات</p>
            </div>
        <?php else: ?>
            <div class="row product-grid">
                <?php foreach($products as $product): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <?php
                            // التحقق من وجود الصورة
                            $productImagePath = ROOT_PATH . '/public/uploads/products/' . $product['image'];
                            $productImageUrl = APP_URL . '/public/uploads/products/' . $product['image'];

                            if (!file_exists($productImagePath) || empty($product['image'])) {
                                // إذا لم تكن الصورة موجودة، استخدم الصورة الافتراضية
                                $defaultImagePath = ROOT_PATH . '/public/img/default_product.png';
                                $productImageUrl = APP_URL . '/public/img/default_product.png';

                                if (!file_exists($defaultImagePath)) {
                                    // إذا لم تكن الصورة الافتراضية موجودة، استخدم صورة من الإنترنت
                                    $productImageUrl = 'https://via.placeholder.com/300x200?text=No+Image';
                                }
                            }
                            ?>
                            <img src="<?php echo $productImageUrl; ?>" class="card-img-top" alt="<?php echo $product['name']; ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $product['name']; ?></h5>
                                <?php if (isset($product['category_name'])): ?>
                                    <p class="card-text text-muted"><?php echo $product['category_name']; ?></p>
                                <?php else: ?>
                                    <?php
                                    // الحصول على اسم الفئة من جدول الفئات
                                    $productCategoryName = '';
                                    if (isset($product['category_id'])) {
                                        $sql = "SELECT name FROM categories WHERE id = ?";
                                        $stmt = $conn->prepare($sql);
                                        $stmt->bind_param("i", $product['category_id']);
                                        $stmt->execute();
                                        $result = $stmt->get_result();
                                        if ($result && $result->num_rows > 0) {
                                            $productCategoryName = $result->fetch_assoc()['name'];
                                        }
                                    }
                                    ?>
                                    <p class="card-text text-muted"><?php echo $productCategoryName; ?></p>
                                <?php endif; ?>
                                <p class="card-text"><?php echo number_format($product['price'], 2); ?> ريال</p>
                                <p class="card-text small"><?php echo substr($product['description'], 0, 80) . '...'; ?></p>
                            </div>
                            <div class="card-footer bg-white d-flex justify-content-between">
                                <a href="<?php echo APP_URL; ?>/public/product.php?id=<?php echo $product['id']; ?>" class="btn btn-sm btn-outline-primary">التفاصيل</a>
                                <?php if (isLoggedIn() && isCustomer()): ?>
                                    <button class="btn btn-sm btn-primary add-to-cart" data-product-id="<?php echo $product['id']; ?>">
                                        <i class="fas fa-cart-plus"></i> إضافة للسلة
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- قسم التقييمات -->
    <div class="row mt-5">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">تقييمات المتجر</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- ملخص التقييمات -->
                        <div class="col-md-4">
                            <div class="text-center mb-4">
                                <h2 class="display-4"><?php echo number_format($averageRating, 1); ?></h2>
                                <div class="stars mb-2">
                                    <?php
                                    $fullStars = floor($averageRating);
                                    $halfStar = $averageRating - $fullStars >= 0.5;

                                    for ($i = 1; $i <= 5; $i++) {
                                        if ($i <= $fullStars) {
                                            echo '<i class="fas fa-star text-warning"></i>';
                                        } elseif ($i == $fullStars + 1 && $halfStar) {
                                            echo '<i class="fas fa-star-half-alt text-warning"></i>';
                                        } else {
                                            echo '<i class="far fa-star text-warning"></i>';
                                        }
                                    }
                                    ?>
                                </div>
                                <p class="text-muted">من <?php echo $ratingCount; ?> تقييم</p>
                            </div>

                            <!-- توزيع التقييمات -->
                            <div class="rating-bars">
                                <?php for ($i = 5; $i >= 1; $i--): ?>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="me-2"><?php echo $i; ?> <i class="fas fa-star text-warning small"></i></div>
                                        <div class="progress flex-grow-1" style="height: 10px;">
                                            <?php
                                            $percentage = $ratingCount > 0 ? ($ratingDistribution[$i] / $ratingCount) * 100 : 0;
                                            ?>
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo $percentage; ?>%" aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <div class="ms-2"><?php echo $ratingDistribution[$i]; ?></div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>

                        <!-- نموذج إضافة تقييم -->
                        <div class="col-md-8">
                            <?php if (isLoggedIn() && !$userHasReviewed): ?>
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">أضف تقييمك للمتجر</h5>
                                    </div>
                                    <div class="card-body">
                                        <form method="POST" action="">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                                            <div class="mb-3">
                                                <label class="form-label">التقييم</label>
                                                <div class="rating-input">
                                                    <div class="stars">
                                                        <?php for ($i = 5; $i >= 1; $i--): ?>
                                                            <input type="radio" id="star<?php echo $i; ?>" name="rating" value="<?php echo $i; ?>" <?php echo isset($_POST['rating']) && $_POST['rating'] == $i ? 'checked' : ''; ?>>
                                                            <label for="star<?php echo $i; ?>"><i class="fas fa-star"></i></label>
                                                        <?php endfor; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="comment" class="form-label">التعليق</label>
                                                <textarea class="form-control" id="comment" name="comment" rows="3" required><?php echo isset($_POST['comment']) ? $_POST['comment'] : ''; ?></textarea>
                                            </div>

                                            <button type="submit" name="submit_review" class="btn btn-primary">إرسال التقييم</button>
                                        </form>
                                    </div>
                                </div>
                            <?php elseif (isLoggedIn() && $userHasReviewed): ?>
                                <div class="alert alert-info mb-4">
                                    <p class="mb-0">لقد قمت بتقييم هذا المتجر مسبقاً. شكراً لمشاركتك!</p>
                                </div>
                            <?php elseif (!isLoggedIn()): ?>
                                <div class="alert alert-info mb-4">
                                    <p class="mb-0">يرجى <a href="<?php echo APP_URL; ?>/public/login.php">تسجيل الدخول</a> لإضافة تقييم.</p>
                                </div>
                            <?php endif; ?>

                            <!-- قائمة التقييمات -->
                            <h5 class="mb-3">تقييمات المستخدمين (<?php echo $ratingCount; ?>)</h5>

                            <?php if (empty($storeReviews)): ?>
                                <div class="alert alert-info">
                                    <p class="mb-0">لا توجد تقييمات حتى الآن.</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($storeReviews as $review): ?>
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <div class="d-flex align-items-center">
                                                    <?php
                                                    // التحقق من وجود صورة المستخدم
                                                    $userImagePath = PUBLIC_PATH . '/uploads/users/' . ($review['user_image'] ?? 'default.png');
                                                    $userImageUrl = APP_URL . '/public/uploads/users/' . ($review['user_image'] ?? 'default.png');

                                                    if (!file_exists($userImagePath) || empty($review['user_image'])) {
                                                        $defaultImagePath = PUBLIC_PATH . '/public/img/default_user.png';
                                                        $userImageUrl = APP_URL . '/public/img/default_user.png';

                                                        if (!file_exists($defaultImagePath)) {
                                                            $userImageUrl = 'https://via.placeholder.com/40x40?text=User';
                                                        }
                                                    }
                                                    ?>
                                                    <img src="<?php echo $userImageUrl; ?>" class="rounded-circle me-2" width="40" height="40" alt="<?php echo $review['user_name']; ?>" style="object-fit: cover;">
                                                    <div>
                                                        <h6 class="mb-0"><?php echo $review['user_name']; ?></h6>
                                                        <small class="text-muted"><?php echo formatDate($review['created_at']); ?></small>
                                                    </div>
                                                </div>
                                                <div class="stars">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <i class="<?php echo $i <= $review['rating'] ? 'fas' : 'far'; ?> fa-star text-warning"></i>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                            <p class="card-text"><?php echo $review['comment']; ?></p>

                                            <?php if (!empty($review['reply'])): ?>
                                                <div class="bg-light p-3 mt-3 rounded">
                                                    <h6 class="mb-1">رد البائع:</h6>
                                                    <p class="mb-0"><?php echo $review['reply']; ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Variables JavaScript para AJAX -->
<script>
    const APP_URL = '<?php echo APP_URL; ?>';
    const CSRF_TOKEN = '<?php echo generateCSRFToken(); ?>';
</script>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
