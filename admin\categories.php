<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// معالجة إضافة فئة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_category'])) {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/admin/categories.php');
    }
    
    // الحصول على البيانات
    $name = sanitize($_POST['name']);
    $icon = sanitize($_POST['icon']);
    
    // التحقق من البيانات
    if (empty($name) || empty($icon)) {
        $_SESSION['error'] = 'جميع الحقول مطلوبة';
    } else {
        // إضافة الفئة الجديدة
        $sql = "INSERT INTO store_categories (name, icon, created_at, updated_at) VALUES (?, ?, NOW(), NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $name, $icon);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = 'تمت إضافة الفئة بنجاح';
            redirect(APP_URL . '/admin/categories.php');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء إضافة الفئة: ' . $conn->error;
        }
    }
}

// معالجة تحديث فئة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_category'])) {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/admin/categories.php');
    }
    
    // الحصول على البيانات
    $id = intval($_POST['id']);
    $name = sanitize($_POST['name']);
    $icon = sanitize($_POST['icon']);
    
    // التحقق من البيانات
    if (empty($name) || empty($icon)) {
        $_SESSION['error'] = 'جميع الحقول مطلوبة';
    } else {
        // تحديث الفئة
        $sql = "UPDATE store_categories SET name = ?, icon = ?, updated_at = NOW() WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssi", $name, $icon, $id);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = 'تم تحديث الفئة بنجاح';
            redirect(APP_URL . '/admin/categories.php');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء تحديث الفئة: ' . $conn->error;
        }
    }
}

// معالجة حذف فئة
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $id = intval($_GET['id']);
    
    // التحقق من وجود متاجر مرتبطة بالفئة
    $sql = "SELECT COUNT(*) as count FROM stores WHERE category_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    if ($row['count'] > 0) {
        $_SESSION['error'] = 'لا يمكن حذف الفئة لأنها مرتبطة بمتاجر';
        redirect(APP_URL . '/admin/categories.php');
    }
    
    // حذف الفئة
    $sql = "DELETE FROM store_categories WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        $_SESSION['success'] = 'تم حذف الفئة بنجاح';
    } else {
        $_SESSION['error'] = 'حدث خطأ أثناء حذف الفئة: ' . $conn->error;
    }
    
    redirect(APP_URL . '/admin/categories.php');
}

// الحصول على قائمة الفئات
$sql = "SELECT sc.*, (SELECT COUNT(*) FROM stores WHERE category_id = sc.id) as store_count 
        FROM store_categories sc 
        ORDER BY sc.name";
$result = $conn->query($sql);
$categories = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
}

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة فئات المتاجر</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus"></i> إضافة فئة جديدة
                    </button>
                </div>
            </div>
            
            <!-- قائمة الفئات -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">قائمة فئات المتاجر</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الأيقونة</th>
                                    <th>اسم الفئة</th>
                                    <th>عدد المتاجر</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>آخر تحديث</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($categories)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center">لا توجد فئات</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($categories as $category): ?>
                                        <tr>
                                            <td><?php echo $category['id']; ?></td>
                                            <td><i class="<?php echo $category['icon']; ?> fa-2x"></i></td>
                                            <td><?php echo $category['name']; ?></td>
                                            <td><?php echo $category['store_count']; ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($category['created_at'])); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($category['updated_at'])); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-primary edit-category" 
                                                            data-id="<?php echo $category['id']; ?>" 
                                                            data-name="<?php echo $category['name']; ?>" 
                                                            data-icon="<?php echo $category['icon']; ?>"
                                                            data-bs-toggle="modal" data-bs-target="#editCategoryModal">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($category['store_count'] == 0): ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/categories.php?action=delete&id=<?php echo $category['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه الفئة؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <button type="button" class="btn btn-sm btn-danger" disabled title="لا يمكن حذف الفئة لأنها مرتبطة بمتاجر">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- معلومات عن الأيقونات -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات عن الأيقونات</h5>
                </div>
                <div class="card-body">
                    <p>يتم استخدام مكتبة Font Awesome للأيقونات. يمكنك استخدام أي أيقونة من المكتبة عن طريق كتابة اسمها في حقل الأيقونة.</p>
                    <p>مثال: <code>fas fa-utensils</code> للمطاعم، <code>fas fa-shopping-basket</code> للسوبرماركت، إلخ.</p>
                    <p>يمكنك الاطلاع على جميع الأيقونات المتاحة من خلال <a href="https://fontawesome.com/icons" target="_blank">موقع Font Awesome</a>.</p>
                    
                    <h6 class="mt-4">أمثلة على الأيقونات الشائعة:</h6>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-utensils fa-3x mb-2"></i>
                                    <p class="mb-0"><code>fas fa-utensils</code></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-shopping-basket fa-3x mb-2"></i>
                                    <p class="mb-0"><code>fas fa-shopping-basket</code></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-cookie fa-3x mb-2"></i>
                                    <p class="mb-0"><code>fas fa-cookie</code></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-bread-slice fa-3x mb-2"></i>
                                    <p class="mb-0"><code>fas fa-bread-slice</code></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-coffee fa-3x mb-2"></i>
                                    <p class="mb-0"><code>fas fa-coffee</code></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-pizza-slice fa-3x mb-2"></i>
                                    <p class="mb-0"><code>fas fa-pizza-slice</code></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-ice-cream fa-3x mb-2"></i>
                                    <p class="mb-0"><code>fas fa-ice-cream</code></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-fish fa-3x mb-2"></i>
                                    <p class="mb-0"><code>fas fa-fish</code></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة فئة جديدة -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?php echo APP_URL; ?>/admin/categories.php" method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="add_category" value="1">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="addCategoryModalLabel">إضافة فئة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الفئة</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="icon" class="form-label">الأيقونة</label>
                        <input type="text" class="form-control" id="icon" name="icon" placeholder="مثال: fas fa-utensils" required>
                        <div class="form-text">استخدم أسماء أيقونات Font Awesome. مثال: fas fa-utensils</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">معاينة الأيقونة</label>
                        <div class="p-3 border rounded text-center">
                            <i id="icon-preview" class="fa-3x"></i>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل فئة -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?php echo APP_URL; ?>/admin/categories.php" method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="update_category" value="1">
                <input type="hidden" name="id" id="edit-id">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="editCategoryModalLabel">تعديل فئة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit-name" class="form-label">اسم الفئة</label>
                        <input type="text" class="form-control" id="edit-name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-icon" class="form-label">الأيقونة</label>
                        <input type="text" class="form-control" id="edit-icon" name="icon" placeholder="مثال: fas fa-utensils" required>
                        <div class="form-text">استخدم أسماء أيقونات Font Awesome. مثال: fas fa-utensils</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">معاينة الأيقونة</label>
                        <div class="p-3 border rounded text-center">
                            <i id="edit-icon-preview" class="fa-3x"></i>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript لمعاينة الأيقونات -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // معاينة الأيقونة عند إضافة فئة جديدة
    const iconInput = document.getElementById('icon');
    const iconPreview = document.getElementById('icon-preview');
    
    iconInput.addEventListener('input', function() {
        iconPreview.className = this.value + ' fa-3x';
    });
    
    // معاينة الأيقونة عند تعديل فئة
    const editIconInput = document.getElementById('edit-icon');
    const editIconPreview = document.getElementById('edit-icon-preview');
    
    editIconInput.addEventListener('input', function() {
        editIconPreview.className = this.value + ' fa-3x';
    });
    
    // تعبئة بيانات الفئة عند فتح نافذة التعديل
    const editButtons = document.querySelectorAll('.edit-category');
    
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const icon = this.getAttribute('data-icon');
            
            document.getElementById('edit-id').value = id;
            document.getElementById('edit-name').value = name;
            document.getElementById('edit-icon').value = icon;
            editIconPreview.className = icon + ' fa-3x';
        });
    });
});
</script>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
