<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول ودور المستخدم
if (!isLoggedIn() || !isVendor()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كبائع للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// تضمين النماذج المطلوبة
require_once '../models/Store.php';
require_once '../models/Order.php';
require_once '../models/User.php';

// إنشاء كائنات النماذج
$storeModel = new Store($conn);
$orderModel = new Order($conn);
$userModel = new User($conn);

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// الحصول على متجر المستخدم
$store = $storeModel->getByUserId($userId);

// إذا لم يكن للمستخدم متجر، قم بتوجيهه لإنشاء متجر
if (!$store) {
    $_SESSION['info'] = 'يجب إنشاء متجر أولاً للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/vendor/create_store.php');
}

// الحصول على معرف المتجر
$storeId = $store['id'];

// التحقق من وجود معرف الطلب
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'معرف الطلب غير صحيح';
    redirect(APP_URL . '/vendor/orders.php');
}

$orderId = intval($_GET['id']);

// الحصول على بيانات الطلب
$order = $orderModel->getById($orderId);

// التحقق من أن الطلب ينتمي إلى المتجر
if (!$order || $order['store_id'] != $storeId) {
    $_SESSION['error'] = 'لا يمكنك عرض هذا الطلب';
    redirect(APP_URL . '/vendor/orders.php');
}

// الحصول على عناصر الطلب
$orderItems = $orderModel->getOrderItems($orderId);

// الحصول على تاريخ الطلب
$orderHistory = [];
$sql = "SELECT oh.*, u.name as user_name 
        FROM order_history oh
        LEFT JOIN users u ON oh.user_id = u.id
        WHERE oh.order_id = ?
        ORDER BY oh.created_at DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $orderId);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $orderHistory[] = $row;
    }
}

// تضمين الرأس
include_once '../includes/vendor_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تفاصيل الطلب #<?php echo $order['id']; ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="<?php echo APP_URL; ?>/vendor/orders.php" class="btn btn-sm btn-outline-secondary me-2">
                        <i class="fas fa-arrow-right"></i> العودة إلى الطلبات
                    </a>
                    <?php if ($order['status'] != 'completed' && $order['status'] != 'cancelled'): ?>
                        <a href="<?php echo APP_URL; ?>/vendor/update_order_status.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-success">
                            <i class="fas fa-edit"></i> تحديث الحالة
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="row">
                <!-- معلومات الطلب -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">معلومات الطلب</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">رقم الطلب:</th>
                                    <td>#<?php echo $order['id']; ?></td>
                                </tr>
                                <tr>
                                    <th>تاريخ الطلب:</th>
                                    <td><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></td>
                                </tr>
                                <tr>
                                    <th>حالة الطلب:</th>
                                    <td>
                                        <?php
                                        $statusClass = '';
                                        $statusText = '';
                                        switch ($order['status']) {
                                            case 'new':
                                                $statusClass = 'bg-primary';
                                                $statusText = 'جديد';
                                                break;
                                            case 'processing':
                                                $statusClass = 'bg-warning text-dark';
                                                $statusText = 'قيد التجهيز';
                                                break;
                                            case 'ready':
                                                $statusClass = 'bg-info';
                                                $statusText = 'جاهز للتسليم';
                                                break;
                                            case 'delivering':
                                                $statusClass = 'bg-info';
                                                $statusText = 'قيد التوصيل';
                                                break;
                                            case 'completed':
                                                $statusClass = 'bg-success';
                                                $statusText = 'مكتمل';
                                                break;
                                            case 'cancelled':
                                                $statusClass = 'bg-danger';
                                                $statusText = 'ملغي';
                                                break;
                                        }
                                        echo '<span class="badge ' . $statusClass . '">' . $statusText . '</span>';
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>طريقة الدفع:</th>
                                    <td>
                                        <?php
                                        switch ($order['payment_method']) {
                                            case 'cash':
                                                echo 'نقداً عند الاستلام';
                                                break;
                                            case 'card':
                                                echo 'بطاقة ائتمان';
                                                break;
                                            case 'bank_transfer':
                                                echo 'تحويل بنكي';
                                                break;
                                            default:
                                                echo $order['payment_method'];
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>حالة الدفع:</th>
                                    <td>
                                        <?php if ($order['payment_status'] == 'paid'): ?>
                                            <span class="badge bg-success">مدفوع</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning text-dark">غير مدفوع</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>إجمالي المبلغ:</th>
                                    <td><strong><?php echo number_format($order['total_amount'], 2); ?> ريال</strong></td>
                                </tr>
                                <tr>
                                    <th>ملاحظات:</th>
                                    <td><?php echo !empty($order['notes']) ? nl2br(htmlspecialchars($order['notes'])) : 'لا توجد ملاحظات'; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات العميل -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">معلومات العميل</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">اسم العميل:</th>
                                    <td><?php echo $order['user_name']; ?></td>
                                </tr>
                                <tr>
                                    <th>رقم الهاتف:</th>
                                    <td><?php echo $order['phone']; ?></td>
                                </tr>
                                <tr>
                                    <th>عنوان التوصيل:</th>
                                    <td><?php echo nl2br(htmlspecialchars($order['address'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- عناصر الطلب -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">عناصر الطلب</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="80">الصورة</th>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orderItems as $item): ?>
                                    <tr>
                                        <td>
                                            <?php
                                            $imagePath = !empty($item['product_image']) ? APP_URL . '/public/uploads/products/' . $item['product_image'] : APP_URL . '/public/img/default_product.png';
                                            ?>
                                            <img src="<?php echo $imagePath; ?>" alt="<?php echo $item['product_name']; ?>" class="img-thumbnail" width="60">
                                        </td>
                                        <td><?php echo $item['product_name']; ?></td>
                                        <td><?php echo number_format($item['price'], 2); ?> ريال</td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td><?php echo number_format($item['price'] * $item['quantity'], 2); ?> ريال</td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-end">الإجمالي:</th>
                                    <th><?php echo number_format($order['total_amount'], 2); ?> ريال</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- تاريخ الطلب -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">تاريخ الطلب</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($orderHistory)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا يوجد سجل لتاريخ الطلب حتى الآن.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الحالة السابقة</th>
                                        <th>الحالة الجديدة</th>
                                        <th>بواسطة</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orderHistory as $history): ?>
                                        <tr>
                                            <td><?php echo date('Y-m-d H:i', strtotime($history['created_at'])); ?></td>
                                            <td>
                                                <?php
                                                $oldStatusText = '';
                                                switch ($history['old_status']) {
                                                    case 'new':
                                                        $oldStatusText = 'جديد';
                                                        break;
                                                    case 'processing':
                                                        $oldStatusText = 'قيد التجهيز';
                                                        break;
                                                    case 'ready':
                                                        $oldStatusText = 'جاهز للتسليم';
                                                        break;
                                                    case 'delivering':
                                                        $oldStatusText = 'قيد التوصيل';
                                                        break;
                                                    case 'completed':
                                                        $oldStatusText = 'مكتمل';
                                                        break;
                                                    case 'cancelled':
                                                        $oldStatusText = 'ملغي';
                                                        break;
                                                }
                                                echo $oldStatusText;
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $newStatusText = '';
                                                switch ($history['new_status']) {
                                                    case 'new':
                                                        $newStatusText = 'جديد';
                                                        break;
                                                    case 'processing':
                                                        $newStatusText = 'قيد التجهيز';
                                                        break;
                                                    case 'ready':
                                                        $newStatusText = 'جاهز للتسليم';
                                                        break;
                                                    case 'delivering':
                                                        $newStatusText = 'قيد التوصيل';
                                                        break;
                                                    case 'completed':
                                                        $newStatusText = 'مكتمل';
                                                        break;
                                                    case 'cancelled':
                                                        $newStatusText = 'ملغي';
                                                        break;
                                                }
                                                echo $newStatusText;
                                                ?>
                                            </td>
                                            <td><?php echo $history['user_name']; ?></td>
                                            <td><?php echo !empty($history['notes']) ? nl2br(htmlspecialchars($history['notes'])) : '-'; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/vendor_footer.php';
?>
