<?php
// Incluir archivo de configuración si no está incluido
if (!defined('APP_NAME')) {
    require_once '../config/config.php';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?></title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo APP_URL; ?>/public/css/style.css">
    <!-- New Theme CSS -->
    <link rel="stylesheet" href="<?php echo APP_URL; ?>/public/css/theme.css">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?php echo APP_URL; ?>/public/index.php"><?php echo APP_NAME; ?></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo APP_URL; ?>/public/index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo APP_URL; ?>/public/stores.php">المتاجر</a>
                    </li>
                    <?php if (isLoggedIn()): ?>
                        <?php if (isAdmin()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/admin/dashboard.php">لوحة التحكم</a>
                            </li>
                        <?php elseif (isVendor()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/vendor/dashboard.php">لوحة التحكم</a>
                            </li>
                        <?php else: ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/customer/orders.php">طلباتي</a>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <?php if (isCustomer()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo APP_URL; ?>/public/cart.php">
                                    <i class="fas fa-shopping-cart"></i> السلة
                                    <span class="badge bg-danger" id="cart-count">0</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <?php
                                // التحقق من وجود صورة الملف الشخصي
                                $profileImage = isset($_SESSION['profile_image']) ? $_SESSION['profile_image'] : 'default.png';
                                $profileImagePath = ROOT_PATH . '/public/uploads/users/' . $profileImage;
                                $profileImageUrl = APP_URL . '/public/uploads/users/' . $profileImage;

                                if (!file_exists($profileImagePath) || empty($profileImage)) {
                                    // إذا لم تكن الصورة موجودة، استخدم الصورة الافتراضية
                                    $defaultImagePath = ROOT_PATH . '/public/img/default_user.png';
                                    $profileImageUrl = APP_URL . '/public/img/default_user.png';

                                    if (!file_exists($defaultImagePath)) {
                                        // إذا لم تكن الصورة الافتراضية موجودة، استخدم الأيقونة
                                        echo '<i class="fas fa-user me-1"></i>';
                                    } else {
                                        echo '<img src="' . $profileImageUrl . '" class="rounded-circle me-1" width="24" height="24" alt="' . $_SESSION['user_name'] . '" style="object-fit: cover;">';
                                    }
                                } else {
                                    echo '<img src="' . $profileImageUrl . '" class="rounded-circle me-1" width="24" height="24" alt="' . $_SESSION['user_name'] . '" style="object-fit: cover;">';
                                }
                                ?>
                                <?php echo $_SESSION['user_name']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/public/profile.php">الملف الشخصي</a></li>
                                <?php if (isCustomer()): ?>
                                    <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/customer/orders.php">طلباتي</a></li>
                                    <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/customer/reviews.php">تقييماتي</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo APP_URL; ?>/public/logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/public/login.php">تسجيل الدخول</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo APP_URL; ?>/public/register.php">إنشاء حساب</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content Container -->
    <div class="container mt-4">
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php
                    echo $_SESSION['success'];
                    unset($_SESSION['success']);
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php
                    echo $_SESSION['error'];
                    unset($_SESSION['error']);
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
