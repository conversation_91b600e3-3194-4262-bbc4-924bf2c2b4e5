<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// التحقق من وجود معرف الإشعار
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف الإشعار غير صالح';
    redirect(APP_URL . '/public/profile.php');
}

$notificationId = intval($_GET['id']);

// التحقق من أن الإشعار ينتمي للمستخدم الحالي
$sql = "SELECT * FROM notifications WHERE id = ? AND user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("ii", $notificationId, $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    // تحديث حالة الإشعار إلى مقروء
    $sql = "UPDATE notifications SET is_read = 1 WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $notificationId);
    
    if ($stmt->execute()) {
        $_SESSION['success'] = 'تم تحديد الإشعار كمقروء';
    } else {
        $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة الإشعار';
    }
} else {
    $_SESSION['error'] = 'الإشعار غير موجود أو لا ينتمي لك';
}

// إعادة التوجيه إلى صفحة الملف الشخصي
redirect(APP_URL . '/public/profile.php#notifications-tab');
?>
