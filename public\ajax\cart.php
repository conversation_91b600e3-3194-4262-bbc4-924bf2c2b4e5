<?php
/**
 * Controlador AJAX para operaciones del carrito
 */

// Incluir archivo de configuración
require_once '../../config/config.php';

// Incluir modelo de carrito
require_once '../../models/Cart.php';

// Verificar si la solicitud es AJAX
if(!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    die('Acceso directo no permitido');
}

// Verificar token CSRF
if(!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
    echo json_encode(['status' => 'error', 'message' => 'Error de validación CSRF']);
    exit;
}

// Verificar si el usuario está logueado
if(!isLoggedIn() || !isCustomer()) {
    echo json_encode(['status' => 'error', 'message' => 'Debe iniciar sesión como cliente para realizar esta acción']);
    exit;
}

// Inicializar el modelo de carrito
$cartModel = new Cart($conn);

// Obtener la acción solicitada
$action = isset($_POST['action']) ? $_POST['action'] : '';

// Procesar la acción
switch($action) {
    case 'add':
        // Verificar datos requeridos
        if(!isset($_POST['product_id']) || !isset($_POST['quantity'])) {
            echo json_encode(['status' => 'error', 'message' => 'Faltan datos requeridos']);
            exit;
        }
        
        // Obtener datos
        $productId = intval($_POST['product_id']);
        $quantity = intval($_POST['quantity']);
        
        // Validar cantidad
        if($quantity <= 0) {
            echo json_encode(['status' => 'error', 'message' => 'La cantidad debe ser mayor a cero']);
            exit;
        }
        
        // Configurar datos del carrito
        $cartModel->user_id = $_SESSION['user_id'];
        $cartModel->product_id = $productId;
        $cartModel->quantity = $quantity;
        
        // Agregar al carrito
        if($cartModel->addItem()) {
            echo json_encode(['status' => 'success', 'message' => 'Producto agregado al carrito']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Error al agregar el producto al carrito']);
        }
        break;
        
    case 'update':
        // Verificar datos requeridos
        if(!isset($_POST['item_id']) || !isset($_POST['quantity'])) {
            echo json_encode(['status' => 'error', 'message' => 'Faltan datos requeridos']);
            exit;
        }
        
        // Obtener datos
        $itemId = intval($_POST['item_id']);
        $quantity = intval($_POST['quantity']);
        
        // Validar cantidad
        if($quantity <= 0) {
            echo json_encode(['status' => 'error', 'message' => 'La cantidad debe ser mayor a cero']);
            exit;
        }
        
        // Actualizar cantidad
        if($cartModel->updateQuantity($itemId, $quantity, $_SESSION['user_id'])) {
            echo json_encode(['status' => 'success', 'message' => 'Cantidad actualizada']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Error al actualizar la cantidad']);
        }
        break;
        
    case 'remove':
        // Verificar datos requeridos
        if(!isset($_POST['item_id'])) {
            echo json_encode(['status' => 'error', 'message' => 'Faltan datos requeridos']);
            exit;
        }
        
        // Obtener datos
        $itemId = intval($_POST['item_id']);
        
        // Eliminar del carrito
        if($cartModel->removeItem($itemId, $_SESSION['user_id'])) {
            echo json_encode(['status' => 'success', 'message' => 'Producto eliminado del carrito']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Error al eliminar el producto del carrito']);
        }
        break;
        
    case 'count':
        // Obtener cantidad de productos en el carrito
        $count = $cartModel->getCartCount($_SESSION['user_id']);
        echo json_encode(['status' => 'success', 'count' => $count]);
        break;
        
    default:
        echo json_encode(['status' => 'error', 'message' => 'Acción no válida']);
        break;
}
?>
