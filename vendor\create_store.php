<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول ودور المستخدم
if (!isLoggedIn() || !isVendor()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كبائع للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المتجر
require_once '../models/Store.php';

// إنشاء كائن المتجر
$storeModel = new Store($conn);

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// التحقق مما إذا كان المستخدم لديه متجر بالفعل
$store = $storeModel->getByUserId($userId);

if ($store) {
    $_SESSION['info'] = 'لديك متجر بالفعل';
    redirect(APP_URL . '/vendor/dashboard.php');
}

// معالجة إنشاء متجر جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/vendor/create_store.php');
    }

    // الحصول على البيانات المرسلة
    $name = sanitize($_POST['name']);
    $description = sanitize($_POST['description']);
    $phone = sanitize($_POST['phone']);
    $address = sanitize($_POST['address']);
    $category = sanitize($_POST['category']);

    // التحقق من البيانات
    $errors = [];

    if (empty($name)) {
        $errors[] = 'يرجى إدخال اسم المتجر';
    }

    if (empty($description)) {
        $errors[] = 'يرجى إدخال وصف المتجر';
    }

    if (empty($phone)) {
        $errors[] = 'يرجى إدخال رقم هاتف المتجر';
    }

    if (empty($address)) {
        $errors[] = 'يرجى إدخال عنوان المتجر';
    }

    if (empty($category)) {
        $errors[] = 'يرجى اختيار تصنيف المتجر';
    }

    // معالجة تحميل شعار المتجر
    $logo = 'default_store.png'; // الشعار الافتراضي

    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2 ميجابايت

        if (!in_array($_FILES['logo']['type'], $allowed_types)) {
            $errors[] = 'نوع الملف غير مسموح به. يرجى تحميل صورة بصيغة JPG أو PNG أو GIF';
        }

        if ($_FILES['logo']['size'] > $max_size) {
            $errors[] = 'حجم الصورة كبير جداً. الحد الأقصى هو 2 ميجابايت';
        }

        if (empty($errors)) {
            // إنشاء اسم فريد للصورة
            $logo = 'store_' . time() . '_' . $_SESSION['user_id'] . '.' . pathinfo($_FILES['logo']['name'], PATHINFO_EXTENSION);
            $upload_dir = PUBLIC_PATH . '/uploads/stores/';

            // التأكد من وجود المجلد
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $upload_path = $upload_dir . $logo;

            if (!move_uploaded_file($_FILES['logo']['tmp_name'], $upload_path)) {
                $errors[] = 'حدث خطأ أثناء تحميل الصورة';
                $logo = 'default_store.png'; // استخدام الشعار الافتراضي في حالة فشل التحميل
            }
        }
    }

    // إذا لم تكن هناك أخطاء، قم بإنشاء المتجر
    if (empty($errors)) {
        $storeModel->user_id = $userId;
        $storeModel->name = $name;
        $storeModel->description = $description;
        $storeModel->logo = $logo;
        $storeModel->phone = $phone;
        $storeModel->address = $address;
        $storeModel->category_id = 1; // استخدام التصنيف الافتراضي
        $storeModel->is_active = 0; // المتجر غير نشط حتى يتم الموافقة عليه من قبل المدير

        if ($storeModel->create()) {
            $_SESSION['success'] = 'تم إنشاء المتجر بنجاح. سيتم مراجعته من قبل الإدارة قبل تفعيله.';
            redirect(APP_URL . '/vendor/dashboard.php');
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء إنشاء المتجر';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// تضمين الرأس
include_once '../includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">إنشاء متجر جديد</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <p class="mb-0">مرحباً بك في ذوق ماركت! لبدء البيع على منصتنا، يرجى إنشاء متجرك الخاص.</p>
                    </div>

                    <form action="" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المتجر</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($_POST['name']) ? $_POST['name'] : ''; ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">تصنيف المتجر</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">اختر التصنيف</option>
                                    <option value="restaurant" <?php echo isset($_POST['category']) && $_POST['category'] === 'restaurant' ? 'selected' : ''; ?>>مطعم</option>
                                    <option value="supermarket" <?php echo isset($_POST['category']) && $_POST['category'] === 'supermarket' ? 'selected' : ''; ?>>سوبرماركت</option>
                                    <option value="bakery" <?php echo isset($_POST['category']) && $_POST['category'] === 'bakery' ? 'selected' : ''; ?>>مخبز</option>
                                    <option value="cafe" <?php echo isset($_POST['category']) && $_POST['category'] === 'cafe' ? 'selected' : ''; ?>>مقهى</option>
                                    <option value="other" <?php echo isset($_POST['category']) && $_POST['category'] === 'other' ? 'selected' : ''; ?>>أخرى</option>
                                </select>
                            </div>

                            <div class="col-12 mb-3">
                                <label for="description" class="form-label">وصف المتجر</label>
                                <textarea class="form-control" id="description" name="description" rows="4" required><?php echo isset($_POST['description']) ? $_POST['description'] : ''; ?></textarea>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? $_POST['phone'] : ''; ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="logo" class="form-label">شعار المتجر</label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                <div class="form-text">الحد الأقصى للحجم: 2 ميجابايت. الصيغ المسموح بها: JPG, PNG, GIF</div>
                            </div>

                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">عنوان المتجر</label>
                                <textarea class="form-control" id="address" name="address" rows="2" required><?php echo isset($_POST['address']) ? $_POST['address'] : ''; ?></textarea>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">إنشاء المتجر</button>
                            <a href="<?php echo APP_URL; ?>/public/profile.php" class="btn btn-outline-secondary">العودة إلى الملف الشخصي</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/footer.php';
?>
