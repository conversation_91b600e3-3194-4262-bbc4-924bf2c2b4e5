<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelos
require_once '../models/Product.php';
require_once '../models/Store.php';
require_once '../models/Review.php';

// Verificar ID de producto
if(!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'ID de producto no válido';
    redirect(APP_URL . '/public/stores.php');
}

$productId = intval($_GET['id']);

// Inicializar modelos
$productModel = new Product($conn);
$storeModel = new Store($conn);
$reviewModel = new Review($conn);

// Obtener información del producto
$product = $productModel->getById($productId);

// Verificar si el producto existe y está activo
if(!$product || $product['is_active'] != 1) {
    $_SESSION['error'] = 'El producto no existe o no está disponible';
    redirect(APP_URL . '/public/stores.php');
}

// Obtener información de la tienda
$store = $storeModel->getById($product['store_id']);

// Obtener reseñas del producto
$reviews = $reviewModel->getByProductId($productId);

// Calcular promedio de calificaciones
$averageRating = $reviewModel->getAverageRating($productId);

// Obtener distribución de calificaciones
$ratingCount = count($reviews);
$ratingDistribution = [
    5 => 0,
    4 => 0,
    3 => 0,
    2 => 0,
    1 => 0
];

foreach ($reviews as $review) {
    $ratingDistribution[$review['rating']]++;
}

// Verificar si el usuario ya ha calificado este producto
$userHasReviewed = false;
if (isLoggedIn()) {
    $userHasReviewed = $reviewModel->hasUserReviewedProduct($_SESSION['user_id'], $productId);
}

// Procesar envío de reseña
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_review'])) {
    // Verificar si el usuario está logueado
    if (!isLoggedIn()) {
        $_SESSION['error'] = 'يجب تسجيل الدخول لإضافة تقييم';
        redirect(APP_URL . '/public/login.php');
    }

    // Verificar token CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/public/product.php?id=' . $productId);
    }

    // Verificar si el usuario ya ha calificado este producto
    if ($userHasReviewed) {
        $_SESSION['error'] = 'لقد قمت بتقييم هذا المنتج مسبقاً';
        redirect(APP_URL . '/public/product.php?id=' . $productId);
    }

    // Obtener datos del formulario
    $rating = intval($_POST['rating']);
    $comment = sanitize($_POST['comment']);

    // Validar datos
    $errors = [];

    if ($rating < 1 || $rating > 5) {
        $errors[] = 'التقييم يجب أن يكون بين 1 و 5';
    }

    if (empty($comment)) {
        $errors[] = 'يرجى إدخال تعليق';
    }

    // Si no hay errores, guardar la reseña
    if (empty($errors)) {
        $reviewModel->user_id = $_SESSION['user_id'];
        $reviewModel->product_id = $productId;
        $reviewModel->rating = $rating;
        $reviewModel->comment = $comment;

        if ($reviewModel->create()) {
            $_SESSION['success'] = 'تم إضافة التقييم بنجاح';
            redirect(APP_URL . '/public/product.php?id=' . $productId);
        } else {
            $_SESSION['error'] = 'حدث خطأ أثناء إضافة التقييم';
        }
    } else {
        $_SESSION['error'] = implode('<br>', $errors);
    }

    redirect(APP_URL . '/public/product.php?id=' . $productId);
}

// Verificar si la tienda está activa
if(!$store || $store['is_active'] != 1) {
    $_SESSION['error'] = 'La tienda de este producto no está disponible';
    redirect(APP_URL . '/public/stores.php');
}

// Obtener productos relacionados (misma categoría)
$sql = "SELECT p.*, s.name as store_name
        FROM products p
        JOIN stores s ON p.store_id = s.id
        WHERE p.category_id = ? AND p.id != ? AND p.is_active = 1 AND p.store_id = ?
        ORDER BY RAND()
        LIMIT 4";
$stmt = $conn->prepare($sql);
$stmt->bind_param("iii", $product['category_id'], $productId, $product['store_id']);
$stmt->execute();
$result = $stmt->get_result();
$relatedProducts = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $relatedProducts[] = $row;
    }
}

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="row">
    <!-- Detalles del producto -->
    <div class="col-md-9">
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="row">
                    <!-- Imagen del producto -->
                    <div class="col-md-5 mb-4 mb-md-0">
                        <?php
                        // التحقق من وجود الصورة
                        $imageUrl = '';

                        // إذا كان لدينا اسم صورة في قاعدة البيانات
                        if (!empty($product['image']) && $product['image'] != 'default_product.png') {
                            $imagePath = ROOT_PATH . '/public/uploads/products/' . $product['image'];

                            // التحقق من وجود الملف فعلياً
                            if (file_exists($imagePath)) {
                                $imageUrl = APP_URL . '/public/uploads/products/' . $product['image'];
                            }
                        }

                        // إذا لم يتم تعيين عنوان URL للصورة، استخدم الصورة الافتراضية
                        if (empty($imageUrl)) {
                            $defaultImagePath = ROOT_PATH . '/public/img/default_product.png';

                            if (file_exists($defaultImagePath)) {
                                $imageUrl = APP_URL . '/public/img/default_product.png';
                            } else {
                                // إذا لم تكن الصورة الافتراضية موجودة، استخدم صورة من الإنترنت
                                $imageUrl = 'https://via.placeholder.com/600x400?text=No+Image';
                            }
                        }

                        // طباعة مسار الصورة للتصحيح
                        echo '<!-- Debug: Image path = ' . $imagePath . ' -->';
                        ?>
                        <img src="<?php echo $imageUrl; ?>" class="img-fluid rounded product-details-img" alt="<?php echo $product['name']; ?>">
                    </div>

                    <!-- Información del producto -->
                    <div class="col-md-7">
                        <h2 class="mb-3"><?php echo $product['name']; ?></h2>

                        <div class="d-flex align-items-center mb-3">
                            <a href="<?php echo APP_URL; ?>/public/store.php?id=<?php echo $store['id']; ?>" class="text-decoration-none">
                                <?php
                                // التحقق من وجود شعار المتجر
                                $storeLogoPath = PUBLIC_PATH . '/uploads/stores/' . $store['logo'];
                                $storeLogoUrl = APP_URL . '/public/uploads/stores/' . $store['logo'];

                                if (!file_exists($storeLogoPath) || empty($store['logo'])) {
                                    // إذا لم يكن الشعار موجوداً، استخدم الشعار الافتراضي
                                    $defaultStoreLogoPath = PUBLIC_PATH . '/img/default_store.png';
                                    $storeLogoUrl = APP_URL . '/public/img/default_store.png';

                                    if (!file_exists($defaultStoreLogoPath)) {
                                        // إذا لم يكن الشعار الافتراضي موجوداً، استخدم صورة من الإنترنت
                                        $storeLogoUrl = 'https://via.placeholder.com/30x30?text=S';
                                    }
                                }
                                ?>
                                <img src="<?php echo $storeLogoUrl; ?>" class="rounded-circle me-2" width="30" height="30" alt="<?php echo $store['name']; ?>">
                                <span class="text-muted"><?php echo $store['name']; ?></span>
                            </a>
                        </div>

                        <?php
                        // الحصول على اسم الفئة
                        $categoryName = '';
                        if (isset($product['category_name'])) {
                            $categoryName = $product['category_name'];
                        } elseif (isset($product['category_id'])) {
                            $sql = "SELECT name FROM categories WHERE id = ?";
                            $stmt = $conn->prepare($sql);
                            $stmt->bind_param("i", $product['category_id']);
                            $stmt->execute();
                            $result = $stmt->get_result();
                            if ($result && $result->num_rows > 0) {
                                $categoryName = $result->fetch_assoc()['name'];
                            }
                        }
                        ?>
                        <p class="text-muted mb-3">الفئة: <?php echo $categoryName; ?></p>

                        <h4 class="mb-3 text-primary"><?php echo number_format($product['price'], 2); ?> ريال</h4>

                        <p class="mb-4"><?php echo $product['description']; ?></p>

                        <div class="d-flex align-items-center mb-4">
                            <span class="me-3">الكمية:</span>
                            <input type="number" id="quantity" class="form-control quantity-input me-3" value="1" min="1" max="<?php echo $product['stock_quantity']; ?>">
                            <span class="text-muted">(متوفر: <?php echo $product['stock_quantity']; ?>)</span>
                        </div>

                        <?php if (isLoggedIn() && isCustomer()): ?>
                            <button class="btn btn-primary btn-lg add-to-cart" data-product-id="<?php echo $product['id']; ?>">
                                <i class="fas fa-cart-plus me-2"></i> إضافة للسلة
                            </button>
                        <?php elseif (!isLoggedIn()): ?>
                            <a href="<?php echo APP_URL; ?>/public/login.php" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i> سجل دخول للشراء
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Información de la tienda -->
    <div class="col-md-3">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">معلومات المتجر</h5>
            </div>
            <div class="card-body text-center">
                <?php
                // التحقق من وجود شعار المتجر
                $storeLogoPath = PUBLIC_PATH . '/uploads/stores/' . $store['logo'];
                $storeLogoUrl = APP_URL . '/public/uploads/stores/' . $store['logo'];

                if (!file_exists($storeLogoPath) || empty($store['logo'])) {
                    // إذا لم يكن الشعار موجوداً، استخدم الشعار الافتراضي
                    $defaultStoreLogoPath = PUBLIC_PATH . '/img/default_store.png';
                    $storeLogoUrl = APP_URL . '/public/img/default_store.png';

                    if (!file_exists($defaultStoreLogoPath)) {
                        // إذا لم يكن الشعار الافتراضي موجوداً، استخدم صورة من الإنترنت
                        $storeLogoUrl = 'https://via.placeholder.com/100x100?text=Store';
                    }
                }
                ?>
                <img src="<?php echo $storeLogoUrl; ?>" class="store-logo mb-3" alt="<?php echo $store['name']; ?>">
                <h5 class="card-title"><?php echo $store['name']; ?></h5>
                <?php if (isset($store['category_name'])): ?>
                    <p class="card-text text-muted"><?php echo $store['category_name']; ?></p>
                <?php else: ?>
                    <?php
                    // الحصول على اسم الفئة من جدول الفئات
                    $categoryName = '';
                    if (isset($store['category_id'])) {
                        $sql = "SELECT name FROM categories WHERE id = ?";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param("i", $store['category_id']);
                        $stmt->execute();
                        $result = $stmt->get_result();
                        if ($result && $result->num_rows > 0) {
                            $categoryName = $result->fetch_assoc()['name'];
                        }
                    }
                    ?>
                    <p class="card-text text-muted"><?php echo $categoryName; ?></p>
                <?php endif; ?>
                <hr>
                <p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i> <?php echo $store['address']; ?></p>
                <p class="mb-3"><i class="fas fa-phone me-2"></i> <?php echo $store['phone']; ?></p>
                <a href="<?php echo APP_URL; ?>/public/store.php?id=<?php echo $store['id']; ?>" class="btn btn-outline-primary w-100">
                    تصفح المتجر
                </a>
            </div>
        </div>
    </div>

    <!-- قسم التقييمات -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">التقييمات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- ملخص التقييمات -->
                        <div class="col-md-4">
                            <div class="text-center mb-4">
                                <h2 class="display-4"><?php echo number_format($averageRating, 1); ?></h2>
                                <div class="stars mb-2">
                                    <?php
                                    $fullStars = floor($averageRating);
                                    $halfStar = $averageRating - $fullStars >= 0.5;

                                    for ($i = 1; $i <= 5; $i++) {
                                        if ($i <= $fullStars) {
                                            echo '<i class="fas fa-star text-warning"></i>';
                                        } elseif ($i == $fullStars + 1 && $halfStar) {
                                            echo '<i class="fas fa-star-half-alt text-warning"></i>';
                                        } else {
                                            echo '<i class="far fa-star text-warning"></i>';
                                        }
                                    }
                                    ?>
                                </div>
                                <p class="text-muted">من <?php echo $ratingCount; ?> تقييم</p>
                            </div>

                            <!-- توزيع التقييمات -->
                            <div class="rating-bars">
                                <?php for ($i = 5; $i >= 1; $i--): ?>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="me-2"><?php echo $i; ?> <i class="fas fa-star text-warning small"></i></div>
                                        <div class="progress flex-grow-1" style="height: 10px;">
                                            <?php
                                            $percentage = $ratingCount > 0 ? ($ratingDistribution[$i] / $ratingCount) * 100 : 0;
                                            ?>
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo $percentage; ?>%" aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <div class="ms-2"><?php echo $ratingDistribution[$i]; ?></div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>

                        <!-- نموذج إضافة تقييم -->
                        <div class="col-md-8">
                            <?php if (isLoggedIn() && !$userHasReviewed): ?>
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">أضف تقييمك</h5>
                                    </div>
                                    <div class="card-body">
                                        <form method="POST" action="">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                                            <div class="mb-3">
                                                <label class="form-label">التقييم</label>
                                                <div class="rating-input">
                                                    <div class="stars">
                                                        <?php for ($i = 5; $i >= 1; $i--): ?>
                                                            <input type="radio" id="star<?php echo $i; ?>" name="rating" value="<?php echo $i; ?>" <?php echo isset($_POST['rating']) && $_POST['rating'] == $i ? 'checked' : ''; ?>>
                                                            <label for="star<?php echo $i; ?>"><i class="fas fa-star"></i></label>
                                                        <?php endfor; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="comment" class="form-label">التعليق</label>
                                                <textarea class="form-control" id="comment" name="comment" rows="3" required><?php echo isset($_POST['comment']) ? $_POST['comment'] : ''; ?></textarea>
                                            </div>

                                            <button type="submit" name="submit_review" class="btn btn-primary">إرسال التقييم</button>
                                        </form>
                                    </div>
                                </div>
                            <?php elseif (isLoggedIn() && $userHasReviewed): ?>
                                <div class="alert alert-info mb-4">
                                    <p class="mb-0">لقد قمت بتقييم هذا المنتج مسبقاً. شكراً لمشاركتك!</p>
                                </div>
                            <?php elseif (!isLoggedIn()): ?>
                                <div class="alert alert-info mb-4">
                                    <p class="mb-0">يرجى <a href="<?php echo APP_URL; ?>/public/login.php">تسجيل الدخول</a> لإضافة تقييم.</p>
                                </div>
                            <?php endif; ?>

                            <!-- قائمة التقييمات -->
                            <h5 class="mb-3">تقييمات المستخدمين (<?php echo $ratingCount; ?>)</h5>

                            <?php if (empty($reviews)): ?>
                                <div class="alert alert-info">
                                    <p class="mb-0">لا توجد تقييمات حتى الآن.</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($reviews as $review): ?>
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <div class="d-flex align-items-center">
                                                    <?php
                                                    // التحقق من وجود صورة المستخدم
                                                    $userImagePath = PUBLIC_PATH . '/uploads/users/' . ($review['user_image'] ?? 'default.png');
                                                    $userImageUrl = APP_URL . '/public/uploads/users/' . ($review['user_image'] ?? 'default.png');

                                                    if (!file_exists($userImagePath) || empty($review['user_image'])) {
                                                        $defaultImagePath = PUBLIC_PATH . '/public/img/default_user.png';
                                                        $userImageUrl = APP_URL . '/public/img/default_user.png';

                                                        if (!file_exists($defaultImagePath)) {
                                                            $userImageUrl = 'https://via.placeholder.com/40x40?text=User';
                                                        }
                                                    }
                                                    ?>
                                                    <img src="<?php echo $userImageUrl; ?>" class="rounded-circle me-2" width="40" height="40" alt="<?php echo $review['user_name']; ?>" style="object-fit: cover;">
                                                    <div>
                                                        <h6 class="mb-0"><?php echo $review['user_name']; ?></h6>
                                                        <small class="text-muted"><?php echo formatDate($review['created_at']); ?></small>
                                                    </div>
                                                </div>
                                                <div class="stars">
                                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                                        <i class="<?php echo $i <= $review['rating'] ? 'fas' : 'far'; ?> fa-star text-warning"></i>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                            <p class="card-text"><?php echo $review['comment']; ?></p>

                                            <?php if (!empty($review['reply'])): ?>
                                                <div class="bg-light p-3 mt-3 rounded">
                                                    <h6 class="mb-1">رد البائع:</h6>
                                                    <p class="mb-0"><?php echo $review['reply']; ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Productos relacionados -->
<?php if(!empty($relatedProducts)): ?>
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="mb-4">منتجات مشابهة</h3>
            <div class="row product-grid">
                <?php foreach($relatedProducts as $relatedProduct): ?>
                    <div class="col-md-3 mb-4">
                        <div class="card h-100 shadow-sm">
                            <?php
                            // التحقق من وجود الصورة
                            $relatedImageUrl = '';

                            // إذا كان لدينا اسم صورة في قاعدة البيانات
                            if (!empty($relatedProduct['image']) && $relatedProduct['image'] != 'default_product.png') {
                                $relatedImagePath = ROOT_PATH . '/public/uploads/products/' . $relatedProduct['image'];

                                // التحقق من وجود الملف فعلياً
                                if (file_exists($relatedImagePath)) {
                                    $relatedImageUrl = APP_URL . '/public/uploads/products/' . $relatedProduct['image'];
                                }
                            }

                            // إذا لم يتم تعيين عنوان URL للصورة، استخدم الصورة الافتراضية
                            if (empty($relatedImageUrl)) {
                                $defaultImagePath = ROOT_PATH . '/public/img/default_product.png';

                                if (file_exists($defaultImagePath)) {
                                    $relatedImageUrl = APP_URL . '/public/img/default_product.png';
                                } else {
                                    // إذا لم تكن الصورة الافتراضية موجودة، استخدم صورة من الإنترنت
                                    $relatedImageUrl = 'https://via.placeholder.com/300x200?text=No+Image';
                                }
                            }
                            ?>
                            <img src="<?php echo $relatedImageUrl; ?>" class="card-img-top" alt="<?php echo $relatedProduct['name']; ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $relatedProduct['name']; ?></h5>
                                <p class="card-text"><?php echo number_format($relatedProduct['price'], 2); ?> ريال</p>
                            </div>
                            <div class="card-footer bg-white d-flex justify-content-between">
                                <a href="<?php echo APP_URL; ?>/public/product.php?id=<?php echo $relatedProduct['id']; ?>" class="btn btn-sm btn-outline-primary">التفاصيل</a>
                                <?php if (isLoggedIn() && isCustomer()): ?>
                                    <button class="btn btn-sm btn-primary add-to-cart" data-product-id="<?php echo $relatedProduct['id']; ?>">
                                        <i class="fas fa-cart-plus"></i> إضافة للسلة
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Variables JavaScript para AJAX -->
<script>
    const APP_URL = '<?php echo APP_URL; ?>';
    const CSRF_TOKEN = '<?php echo generateCSRFToken(); ?>';
</script>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
