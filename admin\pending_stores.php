<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المتجر
require_once '../models/Store.php';

// إنشاء كائن المتجر
$storeModel = new Store($conn);

// معالجة الإجراءات
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $storeId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    // التحقق من وجود المتجر
    $store = $storeModel->getById($storeId);
    
    if (!$store) {
        $_SESSION['error'] = 'المتجر غير موجود';
        redirect(APP_URL . '/admin/pending_stores.php');
    }
    
    // تنفيذ الإجراء المطلوب
    switch ($action) {
        case 'approve':
            // الموافقة على المتجر
            if ($storeModel->approve($storeId)) {
                // إرسال إشعار للبائع
                $sql = "INSERT INTO notifications (user_id, title, message, is_read, created_at)
                        VALUES (?, 'تمت الموافقة على متجرك', 'تمت الموافقة على متجرك وهو الآن نشط. يمكنك البدء في إضافة المنتجات.', 0, NOW())";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $store['user_id']);
                $stmt->execute();
                
                $_SESSION['success'] = 'تمت الموافقة على المتجر بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء الموافقة على المتجر';
            }
            break;
            
        case 'reject':
            // رفض المتجر
            if ($storeModel->delete($storeId)) {
                // إرسال إشعار للبائع
                $sql = "INSERT INTO notifications (user_id, title, message, is_read, created_at)
                        VALUES (?, 'تم رفض طلب متجرك', 'نأسف لإبلاغك أنه تم رفض طلب متجرك. يرجى التواصل مع الإدارة لمزيد من المعلومات.', 0, NOW())";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $store['user_id']);
                $stmt->execute();
                
                $_SESSION['success'] = 'تم رفض المتجر بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء رفض المتجر';
            }
            break;
    }
    
    redirect(APP_URL . '/admin/pending_stores.php');
}

// الحصول على قائمة المتاجر المعلقة
$pendingStores = $storeModel->getPendingStores();

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">طلبات المتاجر الجديدة</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="<?php echo APP_URL; ?>/admin/stores.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-store"></i> جميع المتاجر
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- قائمة المتاجر المعلقة -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">قائمة طلبات المتاجر الجديدة</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($pendingStores)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد طلبات متاجر جديدة في الوقت الحالي.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>الشعار</th>
                                        <th>اسم المتجر</th>
                                        <th>المالك</th>
                                        <th>الفئة</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($pendingStores as $store): ?>
                                        <tr>
                                            <td><?php echo $store['id']; ?></td>
                                            <td>
                                                <img src="<?php echo APP_URL; ?>/public/uploads/stores/<?php echo $store['logo']; ?>" alt="<?php echo $store['name']; ?>" class="img-thumbnail" width="50">
                                            </td>
                                            <td><?php echo $store['name']; ?></td>
                                            <td><?php echo $store['owner_name']; ?><br><small class="text-muted"><?php echo $store['owner_email']; ?></small></td>
                                            <td><?php echo $store['category_name']; ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($store['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo APP_URL; ?>/admin/store_details.php?id=<?php echo $store['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo APP_URL; ?>/admin/pending_stores.php?action=approve&id=<?php echo $store['id']; ?>" class="btn btn-sm btn-success" title="موافقة" onclick="return confirm('هل أنت متأكد من الموافقة على هذا المتجر؟')">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                    <a href="<?php echo APP_URL; ?>/admin/pending_stores.php?action=reject&id=<?php echo $store['id']; ?>" class="btn btn-sm btn-danger" title="رفض" onclick="return confirm('هل أنت متأكد من رفض هذا المتجر؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                        <i class="fas fa-times"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
