<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelos
require_once '../models/Review.php';
require_once '../models/StoreReview.php';

// Verificar si el usuario está logueado como cliente
if(!isLoggedIn() || !isCustomer()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كعميل لعرض التقييمات';
    redirect(APP_URL . '/public/login.php');
}

$userId = $_SESSION['user_id'];

// Inicializar modelos
$reviewModel = new Review($conn);
$storeReviewModel = new StoreReview($conn);

// Obtener todas las reseñas de productos del usuario
$productReviews = $reviewModel->getByUserId($userId);

// Obtener todas las reseñas de tiendas del usuario
$storeReviews = $storeReviewModel->getByUserId($userId);

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>تقييماتي</h2>
                <a href="<?php echo APP_URL; ?>/public/index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i> العودة إلى الرئيسية
                </a>
            </div>
            
            <!-- Pestañas de navegación -->
            <ul class="nav nav-tabs mb-4" id="reviewsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button" role="tab" aria-controls="products" aria-selected="true">
                        <i class="fas fa-box me-2"></i> تقييمات المنتجات (<?php echo count($productReviews); ?>)
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="stores-tab" data-bs-toggle="tab" data-bs-target="#stores" type="button" role="tab" aria-controls="stores" aria-selected="false">
                        <i class="fas fa-store me-2"></i> تقييمات المتاجر (<?php echo count($storeReviews); ?>)
                    </button>
                </li>
            </ul>
            
            <!-- Contenido de las pestañas -->
            <div class="tab-content" id="reviewsTabsContent">
                <!-- Reseñas de productos -->
                <div class="tab-pane fade show active" id="products" role="tabpanel" aria-labelledby="products-tab">
                    <?php if(empty($productReviews)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لم تقم بإضافة أي تقييمات للمنتجات حتى الآن</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach($productReviews as $review): ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 shadow-sm">
                                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center">
                                                <?php
                                                // التحقق من وجود صورة المنتج
                                                $productImagePath = ROOT_PATH . '/public/uploads/products/' . $review['product_image'];
                                                $productImageUrl = APP_URL . '/public/uploads/products/' . $review['product_image'];
                                                
                                                if (!file_exists($productImagePath) || empty($review['product_image'])) {
                                                    $defaultImagePath = ROOT_PATH . '/public/img/default_product.png';
                                                    $productImageUrl = APP_URL . '/public/img/default_product.png';
                                                    
                                                    if (!file_exists($defaultImagePath)) {
                                                        $productImageUrl = 'https://via.placeholder.com/40x40?text=Product';
                                                    }
                                                }
                                                ?>
                                                <img src="<?php echo $productImageUrl; ?>" class="me-2 rounded" width="40" height="40" alt="<?php echo $review['product_name']; ?>" style="object-fit: cover;">
                                                <div>
                                                    <h6 class="mb-0"><?php echo $review['product_name']; ?></h6>
                                                    <small class="text-muted"><?php echo $review['store_name']; ?></small>
                                                </div>
                                            </div>
                                            <div class="stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="<?php echo $i <= $review['rating'] ? 'fas' : 'far'; ?> fa-star text-warning"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text"><?php echo $review['comment']; ?></p>
                                            <small class="text-muted"><?php echo formatDate($review['created_at']); ?></small>
                                            
                                            <?php if (!empty($review['reply'])): ?>
                                                <div class="bg-light p-3 mt-3 rounded">
                                                    <h6 class="mb-1">رد البائع:</h6>
                                                    <p class="mb-0"><?php echo $review['reply']; ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="card-footer bg-white">
                                            <a href="<?php echo APP_URL; ?>/public/product.php?id=<?php echo $review['product_id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i> عرض المنتج
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Reseñas de tiendas -->
                <div class="tab-pane fade" id="stores" role="tabpanel" aria-labelledby="stores-tab">
                    <?php if(empty($storeReviews)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لم تقم بإضافة أي تقييمات للمتاجر حتى الآن</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach($storeReviews as $review): ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 shadow-sm">
                                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center">
                                                <?php
                                                // التحقق من وجود شعار المتجر
                                                $storeLogoPath = ROOT_PATH . '/public/uploads/stores/' . $review['store_logo'];
                                                $storeLogoUrl = APP_URL . '/public/uploads/stores/' . $review['store_logo'];
                                                
                                                if (!file_exists($storeLogoPath) || empty($review['store_logo'])) {
                                                    $defaultStoreLogoPath = ROOT_PATH . '/public/img/default_store.png';
                                                    $storeLogoUrl = APP_URL . '/public/img/default_store.png';
                                                    
                                                    if (!file_exists($defaultStoreLogoPath)) {
                                                        $storeLogoUrl = 'https://via.placeholder.com/40x40?text=Store';
                                                    }
                                                }
                                                ?>
                                                <img src="<?php echo $storeLogoUrl; ?>" class="me-2 rounded-circle" width="40" height="40" alt="<?php echo $review['store_name']; ?>" style="object-fit: cover;">
                                                <h6 class="mb-0"><?php echo $review['store_name']; ?></h6>
                                            </div>
                                            <div class="stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="<?php echo $i <= $review['rating'] ? 'fas' : 'far'; ?> fa-star text-warning"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text"><?php echo $review['comment']; ?></p>
                                            <small class="text-muted"><?php echo formatDate($review['created_at']); ?></small>
                                            
                                            <?php if (!empty($review['reply'])): ?>
                                                <div class="bg-light p-3 mt-3 rounded">
                                                    <h6 class="mb-1">رد البائع:</h6>
                                                    <p class="mb-0"><?php echo $review['reply']; ?></p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="card-footer bg-white">
                                            <a href="<?php echo APP_URL; ?>/public/store.php?id=<?php echo $review['store_id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-store me-1"></i> زيارة المتجر
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
