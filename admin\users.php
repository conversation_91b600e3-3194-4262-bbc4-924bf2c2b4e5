<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المستخدم
require_once '../models/User.php';

// إنشاء كائن المستخدم
$userModel = new User($conn);

// معالجة الإجراءات
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $userId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    // التحقق من وجود المستخدم
    $user = $userModel->getById($userId);
    
    if (!$user) {
        $_SESSION['error'] = 'المستخدم غير موجود';
        redirect(APP_URL . '/admin/users.php');
    }
    
    // تنفيذ الإجراء المطلوب
    switch ($action) {
        case 'activate':
            // تفعيل المستخدم
            $userModel->id = $userId;
            $userModel->is_active = 1;
            if ($userModel->update()) {
                $_SESSION['success'] = 'تم تفعيل المستخدم بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء تفعيل المستخدم';
            }
            break;
            
        case 'deactivate':
            // تعطيل المستخدم
            $userModel->id = $userId;
            $userModel->is_active = 0;
            if ($userModel->update()) {
                $_SESSION['success'] = 'تم تعطيل المستخدم بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء تعطيل المستخدم';
            }
            break;
            
        case 'delete':
            // حذف المستخدم
            if ($userModel->delete($userId)) {
                $_SESSION['success'] = 'تم حذف المستخدم بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف المستخدم';
            }
            break;
    }
    
    redirect(APP_URL . '/admin/users.php');
}

// الحصول على قائمة المستخدمين
$role = isset($_GET['role']) ? $_GET['role'] : null;
$users = $userModel->getAll($role);

// تضمين الرأس
include_once '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المستخدمين</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="<?php echo APP_URL; ?>/admin/add_user.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-plus"></i> إضافة مستخدم جديد
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- فلتر المستخدمين -->
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">فلتر المستخدمين</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <form action="" method="GET" class="d-flex">
                                <select name="role" class="form-select me-2">
                                    <option value="">جميع المستخدمين</option>
                                    <option value="admin" <?php echo $role === 'admin' ? 'selected' : ''; ?>>المديرين</option>
                                    <option value="vendor" <?php echo $role === 'vendor' ? 'selected' : ''; ?>>البائعين</option>
                                    <option value="customer" <?php echo $role === 'customer' ? 'selected' : ''; ?>>العملاء</option>
                                </select>
                                <button type="submit" class="btn btn-primary">تصفية</button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form action="" method="GET" class="d-flex">
                                <input type="text" name="search" class="form-control me-2" placeholder="بحث عن مستخدم...">
                                <button type="submit" class="btn btn-primary">بحث</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- قائمة المستخدمين -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">قائمة المستخدمين</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهاتف</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center">لا يوجد مستخدمين</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td><?php echo $user['id']; ?></td>
                                            <td><?php echo $user['name']; ?></td>
                                            <td><?php echo $user['email']; ?></td>
                                            <td><?php echo $user['phone']; ?></td>
                                            <td>
                                                <?php
                                                switch ($user['role']) {
                                                    case 'admin':
                                                        echo '<span class="badge bg-danger">مدير</span>';
                                                        break;
                                                    case 'vendor':
                                                        echo '<span class="badge bg-success">بائع</span>';
                                                        break;
                                                    case 'customer':
                                                        echo '<span class="badge bg-primary">عميل</span>';
                                                        break;
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php if ($user['is_active']): ?>
                                                    <span class="badge bg-success">مفعل</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">معطل</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo APP_URL; ?>/admin/edit_user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($user['is_active']): ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/users.php?action=deactivate&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-warning" onclick="return confirm('هل أنت متأكد من تعطيل هذا المستخدم؟')">
                                                            <i class="fas fa-ban"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/users.php?action=activate&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-success" onclick="return confirm('هل أنت متأكد من تفعيل هذا المستخدم؟')">
                                                            <i class="fas fa-check"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                        <a href="<?php echo APP_URL; ?>/admin/users.php?action=delete&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/admin_footer.php';
?>
