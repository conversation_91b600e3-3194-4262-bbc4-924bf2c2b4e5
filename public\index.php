<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir el encabezado
include_once '../includes/header.php';

// Obtener las categorías de tiendas (restaurantes y supermercados)
$sql = "SELECT * FROM store_categories ORDER BY name";
$result = $conn->query($sql);
$categories = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
}

// Obtener tiendas destacadas
$sql = "SELECT s.*, sc.name as category_name
        FROM stores s
        JOIN store_categories sc ON s.category_id = sc.id
        WHERE s.is_active = 1 AND s.is_featured = 1
        ORDER BY s.name
        LIMIT 8";
$result = $conn->query($sql);
$featuredStores = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $featuredStores[] = $row;
    }
}

// Obtener productos destacados
$sql = "SELECT p.*, s.name as store_name
        FROM products p
        JOIN stores s ON p.store_id = s.id
        WHERE p.is_active = 1 AND p.is_featured = 1
        ORDER BY p.created_at DESC
        LIMIT 8";
$result = $conn->query($sql);
$featuredProducts = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $featuredProducts[] = $row;
    }
}
?>

<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6 hero-content" data-aos="fade-up">
                <h1 class="hero-title">ذوق ماركت</h1>
                <p class="hero-subtitle">منصة متكاملة للمطاعم والسوبرماركت مع خدمات الدفع والتوصيل</p>
                <p class="text-white mb-4">اطلب من مطعمك المفضل أو تسوق من السوبرماركت القريب منك بكل سهولة</p>
                <div class="d-flex gap-3">
                    <a href="<?php echo APP_URL; ?>/public/stores.php" class="btn btn-light btn-lg">تصفح المتاجر</a>
                    <a href="<?php echo APP_URL; ?>/public/register.php" class="btn btn-outline-light btn-lg">انضم إلينا</a>
                </div>
            </div>
            <div class="col-md-6 text-center" data-aos="fade-left" data-aos-delay="200">
                <img src="<?php echo APP_URL; ?>/public/images/hero-image.png" alt="ذوق ماركت" class="img-fluid rounded hero-image">
            </div>
        </div>
    </div>
</div>

<!-- Categories Section -->
<section class="py-5 mb-5">
    <div class="container">
        <h2 class="section-title text-center">تصفح حسب الفئة</h2>
        <p class="section-subtitle text-center">اختر من بين مجموعة متنوعة من المطاعم والسوبرماركت المتاحة على منصتنا</p>
        <div class="row justify-content-center">
            <?php if (empty($categories)): ?>
                <div class="col-12 text-center">
                    <p>لم يتم إضافة فئات بعد</p>
                </div>
            <?php else: ?>
                <?php foreach ($categories as $index => $category): ?>
                    <div class="col-md-4 col-lg-3 mb-4" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                        <div class="category-card">
                            <i class="<?php echo $category['icon']; ?> category-icon"></i>
                            <h5 class="category-title"><?php echo $category['name']; ?></h5>
                            <a href="<?php echo APP_URL; ?>/public/stores.php?category=<?php echo $category['id']; ?>" class="stretched-link"></a>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Featured Stores Section -->
<section class="mb-5 py-5" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title text-center">متاجر مميزة</h2>
        <p class="section-subtitle text-center">اكتشف أفضل المتاجر المميزة على منصة ذوق ماركت</p>
        <div class="row">
            <?php if (empty($featuredStores)): ?>
                <div class="col-12 text-center">
                    <p>لم يتم إضافة متاجر مميزة بعد</p>
                </div>
            <?php else: ?>
                <?php foreach ($featuredStores as $index => $store): ?>
                    <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                        <div class="card h-100">
                            <div class="position-absolute top-0 end-0 m-3">
                                <span class="badge bg-accent">مميز</span>
                            </div>
                            <img src="<?php echo APP_URL . '/public/uploads/stores/' . $store['logo']; ?>" class="card-img-top" alt="<?php echo $store['name']; ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $store['name']; ?></h5>
                                <p class="card-text text-muted"><?php echo $store['category_name']; ?></p>
                                <p class="card-text small"><?php echo substr($store['description'], 0, 80) . '...'; ?></p>
                            </div>
                            <div class="card-footer bg-white">
                                <a href="<?php echo APP_URL; ?>/public/store.php?id=<?php echo $store['id']; ?>" class="btn btn-primary w-100">تصفح المتجر</a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <div class="text-center mt-4" data-aos="fade-up">
            <a href="<?php echo APP_URL; ?>/public/stores.php" class="btn btn-outline-primary">عرض جميع المتاجر</a>
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="py-5 mb-5">
    <div class="container">
        <h2 class="section-title text-center">منتجات مميزة</h2>
        <p class="section-subtitle text-center">اكتشف أفضل المنتجات المختارة بعناية من متاجرنا</p>
        <div class="row">
            <?php if (empty($featuredProducts)): ?>
                <div class="col-12 text-center">
                    <p>لم يتم إضافة منتجات مميزة بعد</p>
                </div>
            <?php else: ?>
                <?php foreach ($featuredProducts as $index => $product): ?>
                    <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                        <div class="card h-100">
                            <div class="position-absolute top-0 end-0 m-3">
                                <span class="badge bg-accent">مميز</span>
                            </div>
                            <img src="<?php echo APP_URL . '/public/uploads/products/' . $product['image']; ?>" class="card-img-top" alt="<?php echo $product['name']; ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $product['name']; ?></h5>
                                <p class="card-text text-muted"><?php echo $product['store_name']; ?></p>
                                <p class="card-text discount-price"><?php echo number_format($product['price'], 2); ?> شيكل</p>
                            </div>
                            <div class="card-footer bg-white d-flex justify-content-between">
                                <a href="<?php echo APP_URL; ?>/public/product.php?id=<?php echo $product['id']; ?>" class="btn btn-sm btn-outline-primary">التفاصيل</a>
                                <?php if (isLoggedIn() && isCustomer()): ?>
                                    <button class="btn btn-sm btn-primary add-to-cart" data-product-id="<?php echo $product['id']; ?>">
                                        <i class="fas fa-cart-plus"></i> إضافة للسلة
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <div class="text-center mt-4" data-aos="fade-up">
            <a href="<?php echo APP_URL; ?>/public/products.php" class="btn btn-outline-primary">عرض جميع المنتجات</a>
        </div>
    </div>
</section>

<!-- Special Offers Section -->
<section class="py-5 mb-5" style="background-color: #f8f9fa;">
    <div class="container">
        <h2 class="section-title text-center">عروض خاصة</h2>
        <p class="section-subtitle text-center">استفد من أفضل العروض والخصومات الحصرية على منتجاتنا</p>
        <div class="row">
            <?php
            // هنا يمكن إضافة منتجات بخصومات من قاعدة البيانات في المستقبل
            // للآن سنضيف بعض العروض الوهمية للعرض
            $specialOffers = [];
            if (!empty($featuredProducts) && count($featuredProducts) >= 4) {
                // استخدام أول 4 منتجات مميزة كعروض خاصة
                for ($i = 0; $i < 4; $i++) {
                    $product = $featuredProducts[$i];
                    $originalPrice = $product['price'] * 1.2; // سعر وهمي أعلى بنسبة 20%
                    $discountPercent = 20; // نسبة خصم 20%
                    $product['original_price'] = $originalPrice;
                    $product['discount_percent'] = $discountPercent;
                    $specialOffers[] = $product;
                }
            }
            ?>

            <?php if (empty($specialOffers)): ?>
                <div class="col-12 text-center">
                    <p>لا توجد عروض خاصة حالياً</p>
                </div>
            <?php else: ?>
                <?php foreach ($specialOffers as $index => $offer): ?>
                    <div class="col-md-3 mb-4" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                        <div class="card h-100">
                            <div class="offer-badge">
                                <?php echo $offer['discount_percent']; ?>% خصم
                            </div>
                            <img src="<?php echo APP_URL . '/public/uploads/products/' . $offer['image']; ?>" class="card-img-top" alt="<?php echo $offer['name']; ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $offer['name']; ?></h5>
                                <p class="card-text text-muted"><?php echo $offer['store_name']; ?></p>
                                <p class="card-text mb-0">
                                    <span class="original-price"><?php echo number_format($offer['original_price'], 2); ?> شيكل</span>
                                    <span class="discount-price"><?php echo number_format($offer['price'], 2); ?> شيكل</span>
                                </p>
                            </div>
                            <div class="card-footer bg-white d-flex justify-content-between">
                                <a href="<?php echo APP_URL; ?>/public/product.php?id=<?php echo $offer['id']; ?>" class="btn btn-sm btn-outline-primary">التفاصيل</a>
                                <?php if (isLoggedIn() && isCustomer()): ?>
                                    <button class="btn btn-sm btn-primary add-to-cart" data-product-id="<?php echo $offer['id']; ?>">
                                        <i class="fas fa-cart-plus"></i> إضافة للسلة
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="py-5 mb-5">
    <div class="container">
        <h2 class="section-title text-center" data-aos="fade-up">كيف يعمل ذوق ماركت؟</h2>
        <p class="section-subtitle text-center" data-aos="fade-up" data-aos-delay="100">عملية سهلة وبسيطة للحصول على ما تحتاجه من منصتنا</p>

        <div class="steps-section mt-5">
            <div class="row text-center">
                <div class="col-md-4 mb-4" data-aos="fade-up">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <div class="step-icon-wrapper">
                            <div class="step-icon">
                                <i class="fas fa-store"></i>
                            </div>
                        </div>
                        <h4 class="step-title">اختر متجرك المفضل</h4>
                        <p class="step-description">تصفح المتاجر والمطاعم المتاحة واختر ما يناسبك من بين مجموعة متنوعة من الخيارات</p>
                        <div class="step-arrow">
                            <i class="fas fa-chevron-circle-right"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <div class="step-icon-wrapper">
                            <div class="step-icon">
                                <i class="fas fa-shopping-basket"></i>
                            </div>
                        </div>
                        <h4 class="step-title">أضف منتجاتك للسلة</h4>
                        <p class="step-description">اختر المنتجات التي تريدها وأضفها إلى سلة التسوق بكل سهولة ويسر</p>
                        <div class="step-arrow">
                            <i class="fas fa-chevron-circle-right"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="400">
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <div class="step-icon-wrapper">
                            <div class="step-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                        </div>
                        <h4 class="step-title">استلم طلبك</h4>
                        <p class="step-description">أكمل عملية الدفع واستلم طلبك في أسرع وقت ممكن مع خدمة التوصيل السريعة</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-5" data-aos="fade-up" data-aos-delay="500">
            <a href="<?php echo APP_URL; ?>/public/register.php" class="btn btn-primary btn-lg">ابدأ التسوق الآن</a>
        </div>
    </div>
</section>

<!-- Join As Vendor Section -->
<section class="py-5 mb-5" style="background-color: #f8f9fa;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6" data-aos="fade-right">
                <h2 class="section-title">انضم إلينا كبائع</h2>
                <p class="mb-4">هل تملك مطعماً أو سوبرماركت؟ انضم إلى ذوق ماركت وابدأ في بيع منتجاتك عبر الإنترنت.</p>
                <div class="vendor-section mb-4">
                    <div class="vendor-feature">
                        <i class="fas fa-check"></i>
                        <span>إدارة سهلة للمنتجات والطلبات من خلال لوحة تحكم متطورة</span>
                    </div>
                    <div class="vendor-feature">
                        <i class="fas fa-check"></i>
                        <span>وصول إلى قاعدة عملاء أكبر وزيادة مبيعاتك بشكل ملحوظ</span>
                    </div>
                    <div class="vendor-feature">
                        <i class="fas fa-check"></i>
                        <span>تقارير مفصلة عن المبيعات والإيرادات وسلوك المستخدمين</span>
                    </div>
                    <div class="vendor-feature">
                        <i class="fas fa-check"></i>
                        <span>دعم فني على مدار الساعة لمساعدتك في حل أي مشكلة</span>
                    </div>
                </div>
                <a href="<?php echo APP_URL; ?>/public/vendor-register.php" class="btn btn-primary btn-lg">انضم كبائع الآن</a>
            </div>
            <div class="col-md-6 text-center" data-aos="fade-left">
                <img src="<?php echo APP_URL; ?>/public/images/vendor-illustration.png" alt="انضم كبائع" class="img-fluid rounded shadow-lg" style="border-radius: 15px;">
            </div>
        </div>
    </div>
</section>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
