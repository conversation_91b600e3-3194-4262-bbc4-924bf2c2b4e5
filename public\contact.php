<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// تحديد ملف الإعدادات
$settingsFile = '../config/settings.php';

// قراءة الإعدادات
if (file_exists($settingsFile)) {
    $settings = include($settingsFile);
} else {
    $settings = [
        'site_name' => 'ذوق ماركت',
        'site_description' => 'منصة متكاملة للمطاعم والسوبرماركت مع خدمات الدفع والتوصيل',
        'site_email' => '<EMAIL>',
        'site_phone' => '123456789',
        'site_address' => 'المملكة العربية السعودية',
        'social_facebook' => 'https://facebook.com/zoukmarket',
        'social_twitter' => 'https://twitter.com/zoukmarket',
        'social_instagram' => 'https://instagram.com/zoukmarket',
    ];
}

// معالجة إرسال نموذج الاتصال
$messageSent = false;
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $errors[] = 'خطأ في التحقق من الأمان';
    } else {
        // الحصول على البيانات المرسلة
        $name = sanitize($_POST['name']);
        $email = sanitize($_POST['email']);
        $subject = sanitize($_POST['subject']);
        $message = sanitize($_POST['message']);
        
        // التحقق من البيانات
        if (empty($name)) {
            $errors[] = 'يرجى إدخال الاسم';
        }
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'يرجى إدخال بريد إلكتروني صالح';
        }
        
        if (empty($subject)) {
            $errors[] = 'يرجى إدخال الموضوع';
        }
        
        if (empty($message)) {
            $errors[] = 'يرجى إدخال الرسالة';
        }
        
        // إذا لم تكن هناك أخطاء، حفظ الرسالة في قاعدة البيانات
        if (empty($errors)) {
            $sql = "INSERT INTO contact_messages (name, email, subject, message, created_at) 
                    VALUES (?, ?, ?, ?, NOW())";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssss", $name, $email, $subject, $message);
            
            if ($stmt->execute()) {
                $messageSent = true;
                
                // إرسال إشعار للمدير
                $sql = "INSERT INTO notifications (user_id, title, message, is_read, created_at)
                        VALUES (1, 'رسالة اتصال جديدة', 'تم استلام رسالة اتصال جديدة من " . $name . "', 0, NOW())";
                $conn->query($sql);
            } else {
                $errors[] = 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.';
            }
        }
    }
}

// تضمين الرأس
include_once '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <h1 class="display-4 mb-4">اتصل بنا</h1>
                    <p class="lead">نحن هنا للإجابة على جميع استفساراتك ومساعدتك في أي وقت</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h3 class="mb-0">معلومات الاتصال</h3>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h5>العنوان</h5>
                        <p>
                            <i class="fas fa-map-marker-alt text-primary me-2"></i>
                            <?php echo $settings['site_address']; ?>
                        </p>
                    </div>
                    
                    <div class="mb-4">
                        <h5>اتصل بنا</h5>
                        <p>
                            <i class="fas fa-phone text-primary me-2"></i>
                            <?php echo $settings['site_phone']; ?>
                        </p>
                        <p>
                            <i class="fas fa-envelope text-primary me-2"></i>
                            <?php echo $settings['site_email']; ?>
                        </p>
                    </div>
                    
                    <div class="mb-4">
                        <h5>ساعات العمل</h5>
                        <p>الأحد - الخميس: 9:00 صباحاً - 5:00 مساءً</p>
                        <p>الجمعة - السبت: 10:00 صباحاً - 2:00 مساءً</p>
                    </div>
                    
                    <div>
                        <h5>تابعنا على</h5>
                        <div class="social-links">
                            <a href="<?php echo $settings['social_facebook']; ?>" class="btn btn-outline-primary me-2" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="<?php echo $settings['social_twitter']; ?>" class="btn btn-outline-primary me-2" target="_blank">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="<?php echo $settings['social_instagram']; ?>" class="btn btn-outline-primary me-2" target="_blank">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h3 class="mb-0">أرسل لنا رسالة</h3>
                </div>
                <div class="card-body">
                    <?php if ($messageSent): ?>
                        <div class="alert alert-success">
                            <h4 class="alert-heading">تم إرسال رسالتك بنجاح!</h4>
                            <p>شكراً لتواصلك معنا. سنقوم بالرد عليك في أقرب وقت ممكن.</p>
                            <hr>
                            <p class="mb-0">يمكنك متابعة استفسارك عبر البريد الإلكتروني الذي قمت بإدخاله.</p>
                        </div>
                    <?php else: ?>
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h4 class="alert-heading">حدث خطأ!</h4>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <form action="" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($_POST['name']) ? $_POST['name'] : ''; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($_POST['email']) ? $_POST['email'] : ''; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="subject" class="form-label">الموضوع</label>
                                <input type="text" class="form-control" id="subject" name="subject" value="<?php echo isset($_POST['subject']) ? $_POST['subject'] : ''; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">الرسالة</label>
                                <textarea class="form-control" id="message" name="message" rows="5" required><?php echo isset($_POST['message']) ? $_POST['message'] : ''; ?></textarea>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">إرسال الرسالة</button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h3 class="mb-0">موقعنا</h3>
                </div>
                <div class="card-body p-0">
                    <div class="ratio ratio-16x9">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3624.674457218805!2d46.675291075911326!3d24.713454274868!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e2f03890d489399%3A0xba974d1c98e79fd5!2sRiyadh%20Saudi%20Arabia!5e0!3m2!1sen!2s!4v1682345678901!5m2!1sen!2s" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h3 class="mb-0">الأسئلة الشائعة</h3>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqHeading1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse1" aria-expanded="true" aria-controls="faqCollapse1">
                                    كيف يمكنني التسجيل في ذوق ماركت؟
                                </button>
                            </h2>
                            <div id="faqCollapse1" class="accordion-collapse collapse show" aria-labelledby="faqHeading1" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يمكنك التسجيل في ذوق ماركت بسهولة من خلال النقر على زر "تسجيل" في الصفحة الرئيسية، ثم ملء النموذج بالمعلومات المطلوبة وإنشاء حساب جديد.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqHeading2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse2" aria-expanded="false" aria-controls="faqCollapse2">
                                    ما هي طرق الدفع المتاحة؟
                                </button>
                            </h2>
                            <div id="faqCollapse2" class="accordion-collapse collapse" aria-labelledby="faqHeading2" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نوفر عدة طرق للدفع تشمل: الدفع النقدي عند الاستلام، بطاقات الائتمان، المحافظ الإلكترونية، والتحويل البنكي.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqHeading3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse3" aria-expanded="false" aria-controls="faqCollapse3">
                                    كم تستغرق عملية التوصيل؟
                                </button>
                            </h2>
                            <div id="faqCollapse3" class="accordion-collapse collapse" aria-labelledby="faqHeading3" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    تختلف مدة التوصيل حسب موقعك والمتجر الذي تطلب منه. عادةً ما تتراوح مدة التوصيل بين 30 دقيقة إلى ساعتين للمطاعم، و1-3 أيام للمنتجات الأخرى.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqHeading4">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse4" aria-expanded="false" aria-controls="faqCollapse4">
                                    كيف يمكنني التسجيل كبائع؟
                                </button>
                            </h2>
                            <div id="faqCollapse4" class="accordion-collapse collapse" aria-labelledby="faqHeading4" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    للتسجيل كبائع، يرجى النقر على "تسجيل كبائع" في الصفحة الرئيسية، وملء النموذج بالمعلومات المطلوبة. سيقوم فريقنا بمراجعة طلبك والرد عليك في أقرب وقت ممكن.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faqHeading5">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqCollapse5" aria-expanded="false" aria-controls="faqCollapse5">
                                    هل يمكنني إلغاء طلبي بعد تقديمه؟
                                </button>
                            </h2>
                            <div id="faqCollapse5" class="accordion-collapse collapse" aria-labelledby="faqHeading5" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، يمكنك إلغاء طلبك إذا لم يتم قبوله من قبل المتجر بعد. بمجرد قبول الطلب وبدء تجهيزه، قد لا يكون من الممكن إلغاؤه. يرجى الاتصال بخدمة العملاء للمساعدة.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/footer.php';
?>
