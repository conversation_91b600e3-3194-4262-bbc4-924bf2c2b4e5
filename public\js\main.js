/**
 * Main JavaScript file for Zouk Market
 */

$(document).ready(function() {
    
    // Update cart count on page load
    updateCartCount();
    
    // Add to cart functionality
    $('.add-to-cart').on('click', function() {
        const productId = $(this).data('product-id');
        const quantity = $('#quantity').length ? $('#quantity').val() : 1;
        
        $.ajax({
            url: APP_URL + '/public/ajax/cart.php',
            type: 'POST',
            data: {
                action: 'add',
                product_id: productId,
                quantity: quantity,
                csrf_token: CSRF_TOKEN
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    showToast('تمت الإضافة إلى السلة بنجاح', 'success');
                    updateCartCount();
                } else {
                    showToast(response.message, 'error');
                }
            },
            error: function() {
                showToast('حدث خطأ أثناء إضافة المنتج للسلة', 'error');
            }
        });
    });
    
    // Update cart item quantity
    $('.update-cart-item').on('change', function() {
        const itemId = $(this).data('item-id');
        const quantity = $(this).val();
        
        $.ajax({
            url: APP_URL + '/public/ajax/cart.php',
            type: 'POST',
            data: {
                action: 'update',
                item_id: itemId,
                quantity: quantity,
                csrf_token: CSRF_TOKEN
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    location.reload();
                } else {
                    showToast(response.message, 'error');
                }
            },
            error: function() {
                showToast('حدث خطأ أثناء تحديث السلة', 'error');
            }
        });
    });
    
    // Remove cart item
    $('.remove-cart-item').on('click', function() {
        const itemId = $(this).data('item-id');
        
        $.ajax({
            url: APP_URL + '/public/ajax/cart.php',
            type: 'POST',
            data: {
                action: 'remove',
                item_id: itemId,
                csrf_token: CSRF_TOKEN
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    location.reload();
                } else {
                    showToast(response.message, 'error');
                }
            },
            error: function() {
                showToast('حدث خطأ أثناء حذف المنتج من السلة', 'error');
            }
        });
    });
    
    // Product image preview in admin/vendor panel
    $('#product-image').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#image-preview').attr('src', e.target.result).show();
            }
            reader.readAsDataURL(file);
        }
    });
    
    // Store logo preview in admin/vendor panel
    $('#store-logo').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#logo-preview').attr('src', e.target.result).show();
            }
            reader.readAsDataURL(file);
        }
    });
    
    // Rating stars functionality
    $('.rating-stars i').on('click', function() {
        const rating = $(this).data('rating');
        $('#rating-value').val(rating);
        
        // Update stars UI
        $('.rating-stars i').removeClass('fas').addClass('far');
        $('.rating-stars i').each(function() {
            if ($(this).data('rating') <= rating) {
                $(this).removeClass('far').addClass('fas');
            }
        });
    });
    
    // Address selection in checkout
    $('#use-saved-address').on('change', function() {
        if ($(this).is(':checked')) {
            $('#saved-addresses-container').show();
            $('#new-address-container').hide();
        } else {
            $('#saved-addresses-container').hide();
            $('#new-address-container').show();
        }
    });
    
    // Payment method selection
    $('input[name="payment_method"]').on('change', function() {
        const method = $(this).val();
        $('.payment-details').hide();
        $('#' + method + '-details').show();
    });
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Function to update cart count
function updateCartCount() {
    $.ajax({
        url: APP_URL + '/public/ajax/cart.php',
        type: 'POST',
        data: {
            action: 'count',
            csrf_token: CSRF_TOKEN
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                $('#cart-count').text(response.count);
            }
        }
    });
}

// Function to show toast notifications
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    if ($('#toast-container').length === 0) {
        $('body').append('<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>');
    }
    
    // Set toast color based on type
    let bgClass = 'bg-info';
    if (type === 'success') bgClass = 'bg-success';
    if (type === 'error') bgClass = 'bg-danger';
    if (type === 'warning') bgClass = 'bg-warning';
    
    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toast = `
        <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">ذوق ماركت</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    // Add toast to container
    $('#toast-container').append(toast);
    
    // Initialize and show toast
    const toastElement = new bootstrap.Toast(document.getElementById(toastId), {
        autohide: true,
        delay: 3000
    });
    toastElement.show();
    
    // Remove toast after it's hidden
    $(`#${toastId}`).on('hidden.bs.toast', function() {
        $(this).remove();
    });
}
