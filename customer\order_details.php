<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelos
require_once '../models/Order.php';
require_once '../models/Store.php';

// Verificar si el usuario está logueado como cliente
if(!isLoggedIn() || !isCustomer()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كعميل لعرض تفاصيل الطلب';
    redirect(APP_URL . '/public/login.php');
}

// Verificar ID del pedido
if(!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف الطلب غير صالح';
    redirect(APP_URL . '/customer/orders.php');
}

$orderId = intval($_GET['id']);
$userId = $_SESSION['user_id'];

// Inicializar modelos
$orderModel = new Order($conn);
$storeModel = new Store($conn);

// Obtener información del pedido
$order = $orderModel->getById($orderId);

// Verificar si el pedido existe y pertenece al usuario
if(!$order || $order['user_id'] != $userId) {
    $_SESSION['error'] = 'الطلب غير موجود أو لا يمكنك الوصول إليه';
    redirect(APP_URL . '/customer/orders.php');
}

// Obtener elementos del pedido
$orderItems = $orderModel->getOrderItems($orderId);

// Obtener información de la tienda
$store = $storeModel->getById($order['store_id']);

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-md-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>تفاصيل الطلب #<?php echo $order['id']; ?></h2>
                <a href="<?php echo APP_URL; ?>/customer/orders.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i> العودة إلى الطلبات
                </a>
            </div>
            
            <!-- Información del pedido -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">معلومات الطلب</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>رقم الطلب:</strong> #<?php echo $order['id']; ?></p>
                            <p><strong>تاريخ الطلب:</strong> <?php echo formatDate($order['created_at']); ?></p>
                            <p><strong>حالة الطلب:</strong> <span class="badge bg-<?php echo getOrderStatusBadgeClass($order['status']); ?>"><?php echo getOrderStatusText($order['status']); ?></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>طريقة الدفع:</strong> <?php echo getPaymentMethodText($order['payment_method']); ?></p>
                            <p><strong>حالة الدفع:</strong> <span class="badge bg-<?php echo $order['payment_status'] === 'paid' ? 'success' : 'warning'; ?>"><?php echo $order['payment_status'] === 'paid' ? 'مدفوع' : 'قيد الانتظار'; ?></span></p>
                            <p><strong>المبلغ الإجمالي:</strong> <?php echo number_format($order['total_amount'], 2); ?> ريال</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Información de entrega -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات التوصيل</h5>
                </div>
                <div class="card-body">
                    <p><strong>العنوان:</strong> <?php echo $order['address']; ?></p>
                    <p><strong>رقم الهاتف:</strong> <?php echo $order['phone']; ?></p>
                    <?php if(!empty($order['notes'])): ?>
                        <p><strong>ملاحظات:</strong> <?php echo $order['notes']; ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Elementos del pedido -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">عناصر الطلب</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th width="80">الصورة</th>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orderItems as $item): ?>
                                    <tr>
                                        <td>
                                            <?php
                                            // التحقق من وجود الصورة
                                            $productImagePath = ROOT_PATH . '/public/uploads/products/' . $item['product_image'];
                                            $productImageUrl = APP_URL . '/public/uploads/products/' . $item['product_image'];
                                            
                                            if (!file_exists($productImagePath) || empty($item['product_image'])) {
                                                // إذا لم تكن الصورة موجودة، استخدم الصورة الافتراضية
                                                $defaultImagePath = ROOT_PATH . '/public/img/default_product.png';
                                                $productImageUrl = APP_URL . '/public/img/default_product.png';
                                                
                                                if (!file_exists($defaultImagePath)) {
                                                    // إذا لم تكن الصورة الافتراضية موجودة، استخدم صورة من الإنترنت
                                                    $productImageUrl = 'https://via.placeholder.com/60x60?text=No+Image';
                                                }
                                            }
                                            ?>
                                            <img src="<?php echo $productImageUrl; ?>" alt="<?php echo $item['product_name']; ?>" class="img-thumbnail" width="60">
                                        </td>
                                        <td><?php echo $item['product_name']; ?></td>
                                        <td><?php echo number_format($item['price'], 2); ?> ريال</td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td><?php echo number_format($item['price'] * $item['quantity'], 2); ?> ريال</td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>المجموع:</strong></td>
                                    <td><strong><?php echo number_format($order['total_amount'], 2); ?> ريال</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Información de la tienda -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات المتجر</h5>
                </div>
                <div class="card-body text-center">
                    <?php
                    // التحقق من وجود شعار المتجر
                    $storeLogoPath = ROOT_PATH . '/public/uploads/stores/' . $store['logo'];
                    $storeLogoUrl = APP_URL . '/public/uploads/stores/' . $store['logo'];
                    
                    if (!file_exists($storeLogoPath) || empty($store['logo'])) {
                        // إذا لم يكن الشعار موجوداً، استخدم الشعار الافتراضي
                        $defaultStoreLogoPath = ROOT_PATH . '/public/img/default_store.png';
                        $storeLogoUrl = APP_URL . '/public/img/default_store.png';
                        
                        if (!file_exists($defaultStoreLogoPath)) {
                            // إذا لم يكن الشعار الافتراضي موجوداً، استخدم صورة من الإنترنت
                            $storeLogoUrl = 'https://via.placeholder.com/100x100?text=Store';
                        }
                    }
                    ?>
                    <img src="<?php echo $storeLogoUrl; ?>" alt="<?php echo $store['name']; ?>" class="store-logo mb-3">
                    <h5><?php echo $store['name']; ?></h5>
                    
                    <?php if (isset($store['category_name'])): ?>
                        <p class="text-muted"><?php echo $store['category_name']; ?></p>
                    <?php else: ?>
                        <?php
                        // الحصول على اسم الفئة من جدول الفئات
                        $categoryName = '';
                        if (isset($store['category_id'])) {
                            $sql = "SELECT name FROM categories WHERE id = ?";
                            $stmt = $conn->prepare($sql);
                            $stmt->bind_param("i", $store['category_id']);
                            $stmt->execute();
                            $result = $stmt->get_result();
                            if ($result && $result->num_rows > 0) {
                                $categoryName = $result->fetch_assoc()['name'];
                            }
                        }
                        ?>
                        <p class="text-muted"><?php echo $categoryName; ?></p>
                    <?php endif; ?>
                    
                    <hr>
                    
                    <div class="d-grid gap-2">
                        <a href="<?php echo APP_URL; ?>/public/store.php?id=<?php echo $store['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-store me-2"></i> زيارة المتجر
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Estado del pedido -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">حالة الطلب</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-shopping-cart me-2"></i> تم الطلب</span>
                            <span class="badge bg-success rounded-pill"><i class="fas fa-check"></i></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-cog me-2"></i> قيد التجهيز</span>
                            <span class="badge bg-<?php echo in_array($order['status'], ['processing', 'ready', 'delivering', 'completed']) ? 'success' : 'secondary'; ?> rounded-pill">
                                <?php echo in_array($order['status'], ['processing', 'ready', 'delivering', 'completed']) ? '<i class="fas fa-check"></i>' : ''; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-box me-2"></i> جاهز للتسليم</span>
                            <span class="badge bg-<?php echo in_array($order['status'], ['ready', 'delivering', 'completed']) ? 'success' : 'secondary'; ?> rounded-pill">
                                <?php echo in_array($order['status'], ['ready', 'delivering', 'completed']) ? '<i class="fas fa-check"></i>' : ''; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-truck me-2"></i> قيد التوصيل</span>
                            <span class="badge bg-<?php echo in_array($order['status'], ['delivering', 'completed']) ? 'success' : 'secondary'; ?> rounded-pill">
                                <?php echo in_array($order['status'], ['delivering', 'completed']) ? '<i class="fas fa-check"></i>' : ''; ?>
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-check-circle me-2"></i> تم التسليم</span>
                            <span class="badge bg-<?php echo $order['status'] === 'completed' ? 'success' : 'secondary'; ?> rounded-pill">
                                <?php echo $order['status'] === 'completed' ? '<i class="fas fa-check"></i>' : ''; ?>
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
