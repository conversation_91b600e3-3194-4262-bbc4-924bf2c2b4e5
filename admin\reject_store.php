<?php
// التحقق من تسجيل الدخول ودور المستخدم
require_once '../config/config.php';

// التحقق من تسجيل الدخول كمدير
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كمدير للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/public/login.php');
}

// تضمين نموذج المتجر
require_once '../models/Store.php';

// التحقق من وجود معرف المتجر
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف المتجر غير صالح';
    redirect(APP_URL . '/admin/pending_stores.php');
}

$storeId = intval($_GET['id']);

// إنشاء كائن المتجر
$storeModel = new Store($conn);

// الحصول على بيانات المتجر
$store = $storeModel->getById($storeId);

if (!$store) {
    $_SESSION['error'] = 'المتجر غير موجود';
    redirect(APP_URL . '/admin/pending_stores.php');
}

// رفض المتجر (حذفه)
if ($storeModel->delete($storeId)) {
    // إرسال إشعار للبائع
    $sql = "INSERT INTO notifications (user_id, title, message, is_read, created_at)
            VALUES (?, 'تم رفض طلب متجرك', 'نأسف لإبلاغك أنه تم رفض طلب متجرك. يرجى التواصل مع الإدارة لمزيد من المعلومات.', 0, NOW())";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $store['user_id']);
    $stmt->execute();
    
    $_SESSION['success'] = 'تم رفض المتجر بنجاح';
} else {
    $_SESSION['error'] = 'حدث خطأ أثناء رفض المتجر';
}

redirect(APP_URL . '/admin/pending_stores.php');
?>
