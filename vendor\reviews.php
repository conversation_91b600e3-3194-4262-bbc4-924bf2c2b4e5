<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول ودور المستخدم
if (!isLoggedIn() || !isVendor()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كبائع للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// تضمين النماذج المطلوبة
require_once '../models/Store.php';
require_once '../models/Product.php';
require_once '../models/Review.php';
require_once '../models/StoreReview.php';

// إنشاء كائنات النماذج
$storeModel = new Store($conn);
$productModel = new Product($conn);
$reviewModel = new Review($conn);
$storeReviewModel = new StoreReview($conn);

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// الحصول على متجر المستخدم
$store = $storeModel->getByUserId($userId);

// إذا لم يكن للمستخدم متجر، قم بتوجيهه لإنشاء متجر
if (!$store) {
    $_SESSION['info'] = 'يجب إنشاء متجر أولاً للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/vendor/create_store.php');
}

// الحصول على معرف المتجر
$storeId = $store['id'];

// الحصول على تقييمات المنتجات
$productReviews = $reviewModel->getByStoreId($storeId);

// الحصول على تقييمات المتجر
$storeReviews = $storeReviewModel->getByStoreId($storeId);

// معالجة الرد على تقييم المنتج
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reply_product_review'])) {

    // التحقق من توكن CSRF
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        $_SESSION['error'] = 'خطأ في التحقق من الأمان';
        redirect(APP_URL . '/vendor/reviews.php');
    }

    // الحصول على البيانات المرسلة
    $reviewId = intval($_POST['review_id']);
    $reply = sanitize($_POST['reply']);

    // التحقق من البيانات
    $errors = [];

    if (empty($reply)) {
        $errors[] = 'يرجى إدخال الرد';
    }

    try {
        // التحقق من أن التقييم ينتمي إلى منتج من المتجر
        $review = $reviewModel->getById($reviewId);
        $product = $productModel->getById($review['product_id']);

        if (!$review || !$product || $product['store_id'] != $storeId) {
            $errors[] = 'لا يمكنك الرد على هذا التقييم';
        }

        // إذا لم تكن هناك أخطاء، قم بإضافة الرد
        if (empty($errors)) {
            if ($reviewModel->addReply($reviewId, $reply)) {
                $_SESSION['success'] = 'تم إضافة الرد بنجاح';
            } else {
                $_SESSION['error'] = 'حدث خطأ أثناء إضافة الرد';
            }
        } else {
            $_SESSION['error'] = implode('<br>', $errors);
        }
    } catch (Exception $e) {
        $_SESSION['error'] = 'حدث خطأ أثناء معالجة التقييم: ' . $e->getMessage();
    }

    redirect(APP_URL . '/vendor/reviews.php');
}



// الحصول على التقييمات
$reviews = [];
if ($tableExists) {
    try {
        $reviews = $reviewModel->getByStoreId($storeId);
    } catch (Exception $e) {
        // تجاهل الخطأ واستخدام مصفوفة فارغة
    }
}

// حساب متوسط تقييمات المنتجات
$totalProductRating = 0;
$productRatingCount = count($productReviews);
$productRatingDistribution = [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0];

foreach ($productReviews as $review) {
    $totalProductRating += $review['rating'];
    $productRatingDistribution[$review['rating']]++;
}

$averageProductRating = $productRatingCount > 0 ? $totalProductRating / $productRatingCount : 0;

// حساب متوسط تقييمات المتجر
$totalStoreRating = 0;
$storeRatingCount = count($storeReviews);
$storeRatingDistribution = [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0];

foreach ($storeReviews as $review) {
    $totalStoreRating += $review['rating'];
    $storeRatingDistribution[$review['rating']]++;
}

$averageStoreRating = $storeRatingCount > 0 ? $totalStoreRating / $storeRatingCount : 0;

// تضمين الرأس
include_once '../includes/vendor_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تقييمات العملاء</h1>
            </div>

            <!-- ملخص التقييمات -->
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">ملخص التقييمات</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-4">
                                <div class="display-4 me-3"><?php echo number_format($averageProductRating, 1); ?></div>
                                    <div>
                                        <div class="stars">
                                            <?php
                                            $fullStars = floor($averageProductRating);
                                            $halfStar = $averageProductRating - $fullStars >= 0.5;

                                            for ($i = 1; $i <= 5; $i++) {
                                                if ($i <= $fullStars) {
                                                    echo '<i class="fas fa-star text-warning"></i>';
                                                } elseif ($i == $fullStars + 1 && $halfStar) {
                                                    echo '<i class="fas fa-star-half-alt text-warning"></i>';
                                                } else {
                                                    echo '<i class="far fa-star text-warning"></i>';
                                                }
                                            }
                                            ?>
                                        </div>
                                        <div class="text-muted">من <?php echo $productRatingCount; ?> تقييم</div>
                                    </div>
                                </div>

                                <!-- توزيع التقييمات -->
                                <div class="rating-bars">
                                    <?php for ($i = 5; $i >= 1; $i--): ?>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="me-2"><?php echo $i; ?> <i class="fas fa-star text-warning small"></i></div>
                                            <div class="progress flex-grow-1" style="height: 10px;">
                                                <?php
                                                $percentage = $productRatingCount > 0 ? ($productRatingDistribution[$i] / $productRatingCount) * 100 : 0;
                                                ?>
                                                <div class="progress-bar bg-warning" role="progressbar" style="width: <?php echo $percentage; ?>%" aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                            <div class="ms-2"><?php echo $productRatingDistribution[$i]; ?></div>
                                        </div>
                                    <?php endfor; ?>
                                </div>

                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">نصائح للتعامل مع التقييمات</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    الرد على جميع التقييمات بأسلوب مهني ولطيف
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    شكر العملاء على التقييمات الإيجابية
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    التعامل مع التقييمات السلبية بطريقة بناءة
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    الاستفادة من ملاحظات العملاء لتحسين خدماتك
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    تشجيع العملاء على ترك تقييمات إيجابية
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة التقييمات -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">التقييمات (<?php echo count($reviews); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (!$tableExists): ?>
                        <div class="alert alert-warning">
                            <p class="mb-0">جدول التقييمات غير موجود في قاعدة البيانات. يرجى تنفيذ ملف <code>database/create_reviews_table.sql</code> لإنشاء الجدول.</p>
                        </div>
                    <?php elseif (empty($reviews)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد تقييمات حتى الآن.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($reviews as $review): ?>
                            <div class="card mb-3">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <h6 class="mb-0"><?php echo $review['user_name']; ?></h6>
                                            <div class="text-muted small"><?php echo date('Y-m-d H:i', strtotime($review['created_at'])); ?></div>
                                        </div>
                                        <div class="stars">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <?php if ($i <= $review['rating']): ?>
                                                    <i class="fas fa-star text-warning"></i>
                                                <?php else: ?>
                                                    <i class="far fa-star text-warning"></i>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <?php
                                            $productImagePath = !empty($review['product_image']) ? APP_URL . '/public/uploads/products/' . $review['product_image'] : APP_URL . '/public/img/default_product.png';
                                            ?>
                                            <img src="<?php echo $productImagePath; ?>" alt="<?php echo $review['product_name']; ?>" class="img-thumbnail me-2" width="40">
                                            <a href="<?php echo APP_URL; ?>/public/product.php?id=<?php echo $review['product_id']; ?>" target="_blank">
                                                <?php echo $review['product_name']; ?>
                                            </a>
                                        </div>
                                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($review['comment'])); ?></p>
                                    </div>

                                    <?php if (!empty($review['reply'])): ?>
                                        <div class="bg-light p-3 rounded mb-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-reply text-primary me-2"></i>
                                                <strong>رد المتجر:</strong>
                                            </div>
                                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($review['reply'])); ?></p>
                                        </div>
                                    <?php else: ?>
                                        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse" data-bs-target="#replyForm<?php echo $review['id']; ?>">
                                            <i class="fas fa-reply"></i> الرد على التقييم
                                        </button>

                                        <div class="collapse mt-3" id="replyForm<?php echo $review['id']; ?>">
                                            <form action="" method="POST">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">
                                                <input type="hidden" name="reply_to_review" value="1">

                                                <div class="mb-3">
                                                    <label for="reply<?php echo $review['id']; ?>" class="form-label">الرد على التقييم</label>
                                                    <textarea class="form-control" id="reply<?php echo $review['id']; ?>" name="reply" rows="3" required></textarea>
                                                </div>

                                                <div class="d-flex justify-content-end">
                                                    <button type="button" class="btn btn-outline-secondary me-2" data-bs-toggle="collapse" data-bs-target="#replyForm<?php echo $review['id']; ?>">إلغاء</button>
                                                    <button type="submit" class="btn btn-primary">إرسال الرد</button>
                                                </div>
                                            </form>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/vendor_footer.php';
?>
