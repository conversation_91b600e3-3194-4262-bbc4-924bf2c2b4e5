<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelos
require_once '../models/Order.php';

// Verificar si el usuario está logueado como cliente
if(!isLoggedIn() || !isCustomer()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كعميل لعرض الطلبات';
    redirect(APP_URL . '/public/login.php');
}

$userId = $_SESSION['user_id'];

// Inicializar modelo de pedidos
$orderModel = new Order($conn);

// Obtener todos los pedidos del usuario
$orders = $orderModel->getByUserId($userId);

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>طلباتي</h2>
                <a href="<?php echo APP_URL; ?>/public/index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i> العودة إلى الرئيسية
                </a>
            </div>
            
            <?php if(empty($orders)): ?>
                <div class="alert alert-info">
                    <p class="mb-0">لا توجد طلبات حتى الآن</p>
                </div>
                <div class="text-center mt-4">
                    <a href="<?php echo APP_URL; ?>/public/stores.php" class="btn btn-primary">تصفح المتاجر</a>
                </div>
            <?php else: ?>
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">قائمة الطلبات</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>المتجر</th>
                                        <th>التاريخ</th>
                                        <th>المبلغ</th>
                                        <th>حالة الطلب</th>
                                        <th>حالة الدفع</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($orders as $order): ?>
                                        <tr>
                                            <td>#<?php echo $order['id']; ?></td>
                                            <td><?php echo $order['store_name']; ?></td>
                                            <td><?php echo formatDate($order['created_at']); ?></td>
                                            <td><?php echo number_format($order['total_amount'], 2); ?> شيكل</td>
                                            <td>
                                                <span class="badge bg-<?php echo getOrderStatusBadgeClass($order['status']); ?>">
                                                    <?php echo getOrderStatusText($order['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $order['payment_status'] === 'paid' ? 'success' : 'warning'; ?>">
                                                    <?php echo $order['payment_status'] === 'paid' ? 'مدفوع' : 'قيد الانتظار'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?php echo APP_URL; ?>/customer/order_details.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i> عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
