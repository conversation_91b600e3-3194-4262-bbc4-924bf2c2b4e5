<?php
/**
 * Cart Model
 *
 * Handles all cart-related operations
 */
class Cart {
    private $conn;
    private $table = 'cart_items';

    // Cart properties
    public $id;
    public $user_id;
    public $product_id;
    public $quantity;
    public $created_at;
    public $updated_at;

    /**
     * Constructor
     *
     * @param mysqli $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Add item to cart
     *
     * @return boolean
     */
    public function addItem() {
        // Check if product already exists in cart
        $existingItem = $this->getCartItem($this->user_id, $this->product_id);

        if($existingItem) {
            // Update quantity
            $query = "UPDATE " . $this->table . "
                      SET quantity = quantity + ?,
                          updated_at = NOW()
                      WHERE user_id = ? AND product_id = ?";

            $stmt = $this->conn->prepare($query);
            $stmt->bind_param("iii", $this->quantity, $this->user_id, $this->product_id);
        } else {
            // Create new cart item
            $query = "INSERT INTO " . $this->table . "
                      SET user_id = ?,
                          product_id = ?,
                          quantity = ?,
                          created_at = NOW(),
                          updated_at = NOW()";

            $stmt = $this->conn->prepare($query);
            $stmt->bind_param("iii", $this->user_id, $this->product_id, $this->quantity);
        }

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Update cart item quantity
     *
     * @param int $id
     * @param int $quantity
     * @param int $userId
     * @return boolean
     */
    public function updateQuantity($id, $quantity, $userId) {
        // Create query
        $query = "UPDATE " . $this->table . "
                  SET quantity = ?,
                      updated_at = NOW()
                  WHERE id = ? AND user_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bind_param("iii", $quantity, $id, $userId);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Remove item from cart
     *
     * @param int $id
     * @param int $userId
     * @return boolean
     */
    public function removeItem($id, $userId) {
        // Create query
        $query = "DELETE FROM " . $this->table . "
                  WHERE id = ? AND user_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bind_param("ii", $id, $userId);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Get cart items by user ID
     *
     * @param int $userId
     * @return array
     */
    public function getCartItems($userId) {
        // Create query
        $query = "SELECT ci.*, p.name, p.price, p.image, p.stock_quantity, s.id as store_id, s.name as store_name
                  FROM " . $this->table . " ci
                  JOIN products p ON ci.product_id = p.id
                  JOIN stores s ON p.store_id = s.id
                  WHERE ci.user_id = ?
                  ORDER BY ci.created_at DESC";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind user ID
        $stmt->bind_param("i", $userId);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        $items = [];

        while($row = $result->fetch_assoc()) {
            $items[] = $row;
        }

        return $items;
    }

    /**
     * Get cart item by user ID and product ID
     *
     * @param int $userId
     * @param int $productId
     * @return array|boolean
     */
    public function getCartItem($userId, $productId) {
        // Create query
        $query = "SELECT * FROM " . $this->table . "
                  WHERE user_id = ? AND product_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind data
        $stmt->bind_param("ii", $userId, $productId);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        if($result->num_rows > 0) {
            return $result->fetch_assoc();
        } else {
            return false;
        }
    }

    /**
     * Get cart count
     *
     * @param int $userId
     * @return int
     */
    public function getCartCount($userId) {
        // Create query
        $query = "SELECT COUNT(*) as count FROM " . $this->table . "
                  WHERE user_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind user ID
        $stmt->bind_param("i", $userId);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        return $row['count'];
    }

    /**
     * Get cart total
     *
     * @param int $userId
     * @return float
     */
    public function getCartTotal($userId) {
        // Create query
        $query = "SELECT SUM(ci.quantity * p.price) as total
                  FROM " . $this->table . " ci
                  JOIN products p ON ci.product_id = p.id
                  WHERE ci.user_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind user ID
        $stmt->bind_param("i", $userId);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        return $row['total'] ?? 0;
    }

    /**
     * Clear cart
     *
     * @param int $userId
     * @return boolean
     */
    public function clearCart($userId) {
        // Create query
        $query = "DELETE FROM " . $this->table . " WHERE user_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind user ID
        $stmt->bind_param("i", $userId);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }

    /**
     * Group cart items by store
     *
     * @param array $cartItems
     * @return array
     */
    public function groupByStore($cartItems) {
        $groupedItems = [];

        foreach($cartItems as $item) {
            $storeId = $item['store_id'];

            if(!isset($groupedItems[$storeId])) {
                $groupedItems[$storeId] = [
                    'store_id' => $storeId,
                    'store_name' => $item['store_name'],
                    'items' => [],
                    'subtotal' => 0
                ];
            }

            $groupedItems[$storeId]['items'][] = $item;
            $groupedItems[$storeId]['subtotal'] += $item['price'] * $item['quantity'];
        }

        return $groupedItems;
    }

    /**
     * Get cart items by store
     *
     * @param int $userId
     * @param int $storeId
     * @return array
     */
    public function getCartItemsByStore($userId, $storeId) {
        // Create query
        $query = "SELECT ci.*, p.name, p.price, p.image, p.stock_quantity
                  FROM " . $this->table . " ci
                  JOIN products p ON ci.product_id = p.id
                  WHERE ci.user_id = ? AND p.store_id = ?
                  ORDER BY ci.created_at DESC";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind parameters
        $stmt->bind_param("ii", $userId, $storeId);

        // Execute query
        $stmt->execute();

        // Get result
        $result = $stmt->get_result();

        $items = [];

        while($row = $result->fetch_assoc()) {
            $items[] = $row;
        }

        return $items;
    }

    /**
     * Clear cart by store
     *
     * @param int $userId
     * @param int $storeId
     * @return boolean
     */
    public function clearCartByStore($userId, $storeId) {
        // Create query
        $query = "DELETE ci FROM " . $this->table . " ci
                  JOIN products p ON ci.product_id = p.id
                  WHERE ci.user_id = ? AND p.store_id = ?";

        // Prepare statement
        $stmt = $this->conn->prepare($query);

        // Bind parameters
        $stmt->bind_param("ii", $userId, $storeId);

        // Execute query
        if($stmt->execute()) {
            return true;
        }

        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);

        return false;
    }
}
