<?php
// Incluir archivo de configuración
require_once '../config/config.php';

// Incluir modelo de carrito
require_once '../models/Cart.php';

// Verificar si el usuario está logueado como cliente
if(!isLoggedIn() || !isCustomer()) {
    $_SESSION['error'] = 'Debe iniciar sesión como cliente para acceder al carrito';
    redirect(APP_URL . '/public/login.php');
}

// Inicializar el modelo de carrito
$cartModel = new Cart($conn);

// Obtener los elementos del carrito
$cartItems = $cartModel->getCartItems($_SESSION['user_id']);

// Agrupar elementos por tienda
$groupedItems = $cartModel->groupByStore($cartItems);

// Calcular el total del carrito
$cartTotal = $cartModel->getCartTotal($_SESSION['user_id']);

// Incluir el encabezado
include_once '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h2 class="mb-4">سلة التسوق</h2>
        
        <?php if(empty($cartItems)): ?>
            <div class="alert alert-info">
                <p class="mb-0">سلة التسوق فارغة</p>
            </div>
            <div class="text-center mt-4">
                <a href="<?php echo APP_URL; ?>/public/stores.php" class="btn btn-primary">تصفح المتاجر</a>
            </div>
        <?php else: ?>
            <!-- Elementos del carrito agrupados por tienda -->
            <?php foreach($groupedItems as $storeId => $storeData): ?>
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><?php echo $storeData['store_name']; ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>المجموع</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($storeData['items'] as $item): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="<?php echo APP_URL; ?>/public/uploads/products/<?php echo $item['image']; ?>" alt="<?php echo $item['name']; ?>" class="img-thumbnail me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                                    <div>
                                                        <h6 class="mb-0"><?php echo $item['name']; ?></h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo number_format($item['price'], 2); ?> شيكل</td>
                                            <td>
                                                <input type="number" min="1" max="<?php echo $item['stock_quantity']; ?>" value="<?php echo $item['quantity']; ?>" class="form-control form-control-sm quantity-input update-cart-item" data-item-id="<?php echo $item['id']; ?>">
                                            </td>
                                            <td><?php echo number_format($item['price'] * $item['quantity'], 2); ?> شيكل</td>
                                            <td>
                                                <button class="btn btn-sm btn-danger remove-cart-item" data-item-id="<?php echo $item['id']; ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="3" class="text-end"><strong>المجموع الفرعي:</strong></td>
                                        <td colspan="2"><strong><?php echo number_format($storeData['subtotal'], 2); ?> شيكل</strong></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        <div class="text-end mt-3">
                            <a href="<?php echo APP_URL; ?>/public/checkout.php?store_id=<?php echo $storeId; ?>" class="btn btn-primary">
                                إتمام الطلب من <?php echo $storeData['store_name']; ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
            
            <!-- Resumen del carrito -->
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">ملخص السلة</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>عدد المتاجر:</span>
                        <strong><?php echo count($groupedItems); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>عدد المنتجات:</span>
                        <strong><?php echo count($cartItems); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>المجموع الكلي:</span>
                        <strong><?php echo number_format($cartTotal, 2); ?> شيكل</strong>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-info-circle"></i> يجب إتمام الطلب لكل متجر على حدة
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Incluir el pie de página
include_once '../includes/footer.php';
?>
