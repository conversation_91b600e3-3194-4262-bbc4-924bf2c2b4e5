<?php
/**
 * Category Model
 */
class Category {
    // Database connection
    private $conn;
    
    // Table name
    private $table = 'categories';
    
    // Properties
    public $id;
    public $name;
    public $description;
    public $image;
    public $parent_id;
    public $is_active;
    
    /**
     * Constructor
     * 
     * @param object $db Database connection
     */
    public function __construct($db) {
        $this->conn = $db;
    }
    
    /**
     * Get all categories
     * 
     * @param boolean $activeOnly Get only active categories
     * @param int $parentId Get categories by parent ID
     * @return array
     */
    public function getAll($activeOnly = true, $parentId = null) {
        // Create query
        $query = "SELECT c.*, p.name as parent_name 
                  FROM " . $this->table . " c
                  LEFT JOIN " . $this->table . " p ON c.parent_id = p.id
                  WHERE 1=1";
        
        // Add filters
        if($activeOnly) {
            $query .= " AND c.is_active = 1";
        }
        
        if($parentId !== null) {
            $query .= " AND c.parent_id " . ($parentId === 0 ? "IS NULL" : "= " . intval($parentId));
        }
        
        $query .= " ORDER BY c.name";
        
        // Execute query
        $result = $this->conn->query($query);
        
        $categories = [];
        
        while($row = $result->fetch_assoc()) {
            $categories[] = $row;
        }
        
        return $categories;
    }
    
    /**
     * Get category by ID
     * 
     * @param int $id
     * @return array|boolean
     */
    public function getById($id) {
        // Create query
        $query = "SELECT c.*, p.name as parent_name 
                  FROM " . $this->table . " c
                  LEFT JOIN " . $this->table . " p ON c.parent_id = p.id
                  WHERE c.id = ?";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind ID
        $stmt->bind_param("i", $id);
        
        // Execute query
        $stmt->execute();
        
        // Get result
        $result = $stmt->get_result();
        
        if($result->num_rows > 0) {
            return $result->fetch_assoc();
        } else {
            return false;
        }
    }
    
    /**
     * Create category
     * 
     * @return boolean
     */
    public function create() {
        // Create query
        $query = "INSERT INTO " . $this->table . " 
                  (name, description, image, parent_id, is_active, created_at, updated_at) 
                  VALUES (?, ?, ?, ?, ?, NOW(), NOW())";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->image = htmlspecialchars(strip_tags($this->image));
        
        // Bind data
        $stmt->bind_param("sssii", 
            $this->name, 
            $this->description, 
            $this->image, 
            $this->parent_id, 
            $this->is_active
        );
        
        // Execute query
        if($stmt->execute()) {
            $this->id = $this->conn->insert_id;
            return true;
        }
        
        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);
        
        return false;
    }
    
    /**
     * Update category
     * 
     * @return boolean
     */
    public function update() {
        // Create query
        $query = "UPDATE " . $this->table . " 
                  SET name = ?, description = ?, image = ?, parent_id = ?, is_active = ?, updated_at = NOW() 
                  WHERE id = ?";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->image = htmlspecialchars(strip_tags($this->image));
        
        // Bind data
        $stmt->bind_param("sssiii", 
            $this->name, 
            $this->description, 
            $this->image, 
            $this->parent_id, 
            $this->is_active, 
            $this->id
        );
        
        // Execute query
        if($stmt->execute()) {
            return true;
        }
        
        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);
        
        return false;
    }
    
    /**
     * Delete category
     * 
     * @param int $id
     * @return boolean
     */
    public function delete($id) {
        // Create query
        $query = "DELETE FROM " . $this->table . " WHERE id = ?";
        
        // Prepare statement
        $stmt = $this->conn->prepare($query);
        
        // Bind ID
        $stmt->bind_param("i", $id);
        
        // Execute query
        if($stmt->execute()) {
            return true;
        }
        
        // Print error if something goes wrong
        printf("Error: %s.\n", $stmt->error);
        
        return false;
    }
}
?>
