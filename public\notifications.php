<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// معالجة تحديد جميع الإشعارات كمقروءة
if (isset($_GET['mark_all_read'])) {
    $sql = "UPDATE notifications SET is_read = 1 WHERE user_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $_SESSION['user_id']);
    
    if ($stmt->execute()) {
        $_SESSION['success'] = 'تم تحديد جميع الإشعارات كمقروءة';
    } else {
        $_SESSION['error'] = 'حدث خطأ أثناء تحديث حالة الإشعارات';
    }
    
    redirect(APP_URL . '/public/notifications.php');
}

// الحصول على الإشعارات
$sql = "SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$notifications = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $notifications[] = $row;
    }
}

// تضمين الرأس
include_once '../includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">الإشعارات</h5>
                    <?php if (!empty($notifications)): ?>
                        <a href="<?php echo APP_URL; ?>/public/notifications.php?mark_all_read=1" class="btn btn-light btn-sm">
                            <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                        </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if (empty($notifications)): ?>
                        <div class="alert alert-info">
                            <p class="mb-0">لا توجد إشعارات.</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group">
                            <?php foreach ($notifications as $notification): ?>
                                <div class="list-group-item list-group-item-action <?php echo $notification['is_read'] ? '' : 'list-group-item-primary'; ?>">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">
                                            <?php if (!$notification['is_read']): ?>
                                                <span class="badge bg-primary me-2">جديد</span>
                                            <?php endif; ?>
                                            <?php echo $notification['title']; ?>
                                        </h5>
                                        <small class="text-muted"><?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?></small>
                                    </div>
                                    <p class="mb-1"><?php echo $notification['message']; ?></p>
                                    <?php if (!$notification['is_read']): ?>
                                        <div class="d-flex justify-content-end">
                                            <a href="<?php echo APP_URL; ?>/public/mark_notification.php?id=<?php echo $notification['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-check"></i> تحديد كمقروء
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <a href="<?php echo APP_URL; ?>/public/profile.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right"></i> العودة إلى الملف الشخصي
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين التذييل
include_once '../includes/footer.php';
?>
