/* Dashboard Styles */
.dashboard-sidebar {
    min-height: 100vh;
    background-color: #343a40;
}

.dashboard-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    margin-bottom: 0.2rem;
    border-radius: 0.25rem;
    transition: all 0.3s;
}

.dashboard-sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.dashboard-sidebar .nav-link.active {
    color: #fff;
    background-color: #007bff;
}

.dashboard-sidebar .nav-link i {
    margin-right: 0.5rem;
}

.sidebar-heading {
    color: rgba(255, 255, 255, 0.5);
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Card styles */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
}

/* Table styles */
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Badge styles */
.badge {
    padding: 0.4em 0.6em;
    font-weight: 500;
}

/* Button styles */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Chart container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .dashboard-sidebar {
        min-height: auto;
        margin-bottom: 1rem;
    }
}
