/**
 * Zouk Market - Admin Theme Switcher
 */
document.addEventListener('DOMContentLoaded', function() {
    // Theme options
    const themes = [
        { name: 'blue', label: 'أزرق' },
        { name: 'red', label: 'أحمر' },
        { name: 'green', label: 'أخضر' },
        { name: 'purple', label: 'بنفسجي' },
        { name: 'orange', label: 'برتقالي' }
    ];
    
    // Create theme switcher
    function createThemeSwitcher() {
        const themeSwitcher = document.createElement('div');
        themeSwitcher.className = 'theme-switcher';
        
        // Create toggle button
        const toggleBtn = document.createElement('div');
        toggleBtn.className = 'toggle-btn';
        toggleBtn.innerHTML = '<i class="fas fa-palette"></i>';
        toggleBtn.addEventListener('click', function() {
            themeSwitcher.classList.toggle('collapsed');
        });
        
        // Create theme options
        themes.forEach(theme => {
            const themeOption = document.createElement('div');
            themeOption.className = `theme-option theme-${theme.name}`;
            themeOption.title = theme.label;
            themeOption.dataset.theme = theme.name;
            
            // Check if this is the active theme
            const currentTheme = localStorage.getItem('admin-theme') || 'blue';
            if (theme.name === currentTheme) {
                themeOption.classList.add('active');
                document.body.className = `theme-${theme.name}`;
            }
            
            // Add click event
            themeOption.addEventListener('click', function() {
                // Remove active class from all options
                document.querySelectorAll('.theme-option').forEach(option => {
                    option.classList.remove('active');
                });
                
                // Add active class to clicked option
                themeOption.classList.add('active');
                
                // Apply theme
                document.body.className = `theme-${theme.name}`;
                
                // Save theme preference
                localStorage.setItem('admin-theme', theme.name);
            });
            
            themeSwitcher.appendChild(themeOption);
        });
        
        themeSwitcher.appendChild(toggleBtn);
        document.body.appendChild(themeSwitcher);
    }
    
    // Apply saved theme on page load
    function applySavedTheme() {
        const savedTheme = localStorage.getItem('admin-theme');
        if (savedTheme) {
            document.body.className = `theme-${savedTheme}`;
        }
    }
    
    // Initialize
    createThemeSwitcher();
    applySavedTheme();
    
    // Apply theme to stats cards
    function updateStatsCards() {
        // Convert regular cards to stats cards
        const statCards = document.querySelectorAll('.col-md-3 .card');
        statCards.forEach((card, index) => {
            if (!card.classList.contains('stats-card')) {
                card.classList.add('stats-card');
                
                // Add appropriate class based on current color
                if (card.classList.contains('bg-primary')) {
                    card.classList.add('primary');
                } else if (card.classList.contains('bg-success')) {
                    card.classList.add('success');
                } else if (card.classList.contains('bg-warning')) {
                    card.classList.add('warning');
                } else if (card.classList.contains('bg-danger')) {
                    card.classList.add('danger');
                }
                
                // Add icon class
                const icon = card.querySelector('.fa-2x');
                if (icon) {
                    icon.classList.add('icon');
                }
            }
        });
    }
    
    // Update stats cards
    updateStatsCards();
});
