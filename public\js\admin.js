/**
 * JavaScript file for Zouk Market Admin Panel
 */

$(document).ready(function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert-dismissible').alert('close');
    }, 5000);
    
    // Confirm delete actions
    $('.confirm-delete').on('click', function(e) {
        if (!confirm('هل أنت متأكد من حذف هذا العنصر؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            e.preventDefault();
        }
    });
    
    // Image preview for file inputs
    $('.custom-file-input').on('change', function() {
        const file = this.files[0];
        const preview = $(this).data('preview');
        
        if (file && preview) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $(preview).attr('src', e.target.result).show();
            }
            reader.readAsDataURL(file);
        }
    });
    
    // Toggle sidebar on mobile
    $('#sidebarToggle').on('click', function() {
        $('.dashboard-sidebar').toggleClass('show');
    });
    
    // Select all checkboxes
    $('#selectAll').on('click', function() {
        $('.item-checkbox').prop('checked', $(this).prop('checked'));
        updateBulkActionButtons();
    });
    
    // Update bulk action buttons based on selection
    $('.item-checkbox').on('change', function() {
        updateBulkActionButtons();
    });
    
    // Function to update bulk action buttons
    function updateBulkActionButtons() {
        const checkedCount = $('.item-checkbox:checked').length;
        if (checkedCount > 0) {
            $('.bulk-action-btn').prop('disabled', false);
            $('.selected-count').text(checkedCount);
        } else {
            $('.bulk-action-btn').prop('disabled', true);
            $('.selected-count').text('0');
        }
    }
    
    // Handle bulk actions
    $('.bulk-action-btn').on('click', function() {
        const action = $(this).data('action');
        const selectedIds = [];
        
        $('.item-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });
        
        if (selectedIds.length === 0) {
            alert('الرجاء تحديد عنصر واحد على الأقل');
            return;
        }
        
        // Confirm action
        if (confirm(`هل أنت متأكد من تنفيذ إجراء "${$(this).text()}" على ${selectedIds.length} عنصر؟`)) {
            // Submit form with selected IDs and action
            $('#bulkActionForm input[name="ids"]').val(selectedIds.join(','));
            $('#bulkActionForm input[name="action"]').val(action);
            $('#bulkActionForm').submit();
        }
    });
    
    // Date range picker initialization
    if ($.fn.daterangepicker) {
        $('.date-range-picker').daterangepicker({
            locale: {
                format: 'YYYY-MM-DD',
                applyLabel: 'تطبيق',
                cancelLabel: 'إلغاء',
                fromLabel: 'من',
                toLabel: 'إلى',
                customRangeLabel: 'مخصص',
                daysOfWeek: ['أحد', 'إثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],
                monthNames: ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                firstDay: 0
            },
            ranges: {
                'اليوم': [moment(), moment()],
                'أمس': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'آخر 7 أيام': [moment().subtract(6, 'days'), moment()],
                'آخر 30 يوم': [moment().subtract(29, 'days'), moment()],
                'هذا الشهر': [moment().startOf('month'), moment().endOf('month')],
                'الشهر الماضي': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        });
    }
    
    // DataTables initialization
    if ($.fn.DataTable) {
        $('.datatable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/ar.json'
            },
            responsive: true
        });
    }
    
    // CKEditor initialization
    if (typeof ClassicEditor !== 'undefined') {
        document.querySelectorAll('.ckeditor').forEach(editor => {
            ClassicEditor
                .create(editor, {
                    language: 'ar',
                    toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', '|', 'outdent', 'indent', '|', 'imageUpload', 'blockQuote', 'insertTable', 'mediaEmbed', 'undo', 'redo']
                })
                .catch(error => {
                    console.error(error);
                });
        });
    }
});
