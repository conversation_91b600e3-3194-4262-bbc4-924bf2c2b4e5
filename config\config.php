<?php
/**
 * Archivo de configuración principal
 *
 * Contiene constantes y configuraciones globales para la aplicación
 */

// Configuración de la aplicación
define('APP_NAME', 'ذوق ماركت');
define('APP_URL', 'http://localhost/zouk_market');

// Configuración de rutas
define('ROOT_PATH', dirname(__DIR__));
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('UPLOADS_PATH', PUBLIC_PATH . '/uploads');
define('VIEWS_PATH', ROOT_PATH . '/views');

// Configuración de sesión
session_start();

// Zona horaria
date_default_timezone_set('Asia/Riyadh');

// Funciones de utilidad global
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * Formatear fecha
 *
 * @param string $date
 * @return string
 */
function formatDate($date) {
    return date('d/m/Y H:i', strtotime($date));
}

/**
 * Obtener texto de estado de pedido
 *
 * @param string $status
 * @return string
 */
function getOrderStatusText($status) {
    $statusText = [
        'new' => 'جديد',
        'processing' => 'قيد التجهيز',
        'ready' => 'جاهز للتسليم',
        'delivering' => 'قيد التوصيل',
        'completed' => 'مكتمل',
        'cancelled' => 'ملغي'
    ];

    return $statusText[$status] ?? $status;
}

/**
 * Obtener clase de badge para estado de pedido
 *
 * @param string $status
 * @return string
 */
function getOrderStatusBadgeClass($status) {
    $statusClass = [
        'new' => 'info',
        'processing' => 'primary',
        'ready' => 'warning',
        'delivering' => 'primary',
        'completed' => 'success',
        'cancelled' => 'danger'
    ];

    return $statusClass[$status] ?? 'secondary';
}

/**
 * Obtener texto de método de pago
 *
 * @param string $method
 * @return string
 */
function getPaymentMethodText($method) {
    $methodText = [
        'cash' => 'الدفع عند الاستلام',
        'card' => 'بطاقة ائتمان',
        'bank_transfer' => 'تحويل بنكي'
    ];

    return $methodText[$method] ?? $method;
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getUserRole() {
    return isset($_SESSION['user_role']) ? $_SESSION['user_role'] : null;
}

function isAdmin() {
    return getUserRole() === 'admin';
}

function isVendor() {
    return getUserRole() === 'vendor';
}

function isCustomer() {
    return getUserRole() === 'customer';
}

// Función para sanitizar entradas
function sanitize($input) {
    if (is_array($input)) {
        foreach ($input as $key => $value) {
            $input[$key] = sanitize($value);
        }
    } else {
        $input = htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    return $input;
}

// Función para generar token CSRF
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Función para verificar token CSRF
function verifyCSRFToken($token) {
    if (!isset($_SESSION['csrf_token']) || $token !== $_SESSION['csrf_token']) {
        die('CSRF token validation failed');
    }
    return true;
}

// Incluir archivo de conexión a la base de datos
$conn = require_once 'database.php';
?>
