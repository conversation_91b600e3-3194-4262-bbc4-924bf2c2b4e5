# ذوق ماركت - نظام اتصالات الطوارئ

## نظام اتصالات الطوارئ في حالات الحروب والكوارث
### دراسة تطبيقية على الحرب في قطاع غزة

---

## معلومات المشروع

**البحث الأكاديمي:** نظام اتصالات الطوارئ في حالات الحروب والكوارث - دراسة تطبيقية على الحرب في قطاع غزة

**الباحثة:** هبة محمد عاشور عبد الرحمن  
**المشرف الأكاديمي:** د. طارق عبيد  
**رقم الاتصال:** +972 59-704-9890  
**الموقع:** قطاع غزة - فلسطين

---

## وصف المشروع

ذوق ماركت هو نظام متكامل تم تطويره كجزء من بحث أكاديمي متخصص في مجال اتصالات الطوارئ. يهدف المشروع إلى دراسة وتحليل التحديات التي تواجه أنظمة الاتصالات في حالات الحروب والكوارث، مع التطبيق العملي على الوضع في قطاع غزة.

### الأهداف الرئيسية:

#### الأهداف الأكاديمية:
- دراسة أنظمة الاتصالات في حالات الطوارئ
- تحليل التحديات في قطاع غزة
- تقييم فعالية الحلول المقترحة
- توثيق النتائج والتوصيات

#### الأهداف التطبيقية:
- تطوير نظام عملي للطوارئ
- خدمة المجتمع المحلي
- تعزيز الأمان والاستجابة السريعة
- توفير منصة موثوقة للتواصل

---

## التقنيات المستخدمة

- **PHP** - لغة البرمجة الخلفية
- **MySQL** - قاعدة البيانات
- **Bootstrap 5** - إطار العمل للواجهة الأمامية
- **JavaScript/jQuery** - التفاعل الأمامي
- **HTML5/CSS3** - هيكل وتصميم الصفحات
- **Font Awesome** - الأيقونات
- **AOS** - مكتبة الرسوم المتحركة

---

## ميزات النظام

### للعملاء:
- تصفح المنتجات والمتاجر
- إضافة المنتجات للسلة
- إتمام عمليات الشراء
- تتبع الطلبات
- تقييم المنتجات والمتاجر
- إدارة الملف الشخصي

### للبائعين:
- إنشاء وإدارة المتاجر
- إضافة وتعديل المنتجات
- إدارة الطلبات
- عرض التقارير والإحصائيات
- إدارة المراجعات

### للإدارة:
- إدارة المستخدمين والمتاجر
- مراقبة النظام
- إدارة الطلبات
- عرض التقارير الشاملة
- إدارة الإعدادات

---

## هيكل المشروع

```
zouk_market/
├── admin/              # ملفات لوحة تحكم الإدارة
├── config/             # ملفات الإعدادات
├── customer/           # ملفات العملاء
├── database/           # ملفات قاعدة البيانات
├── includes/           # الملفات المشتركة
├── models/             # نماذج البيانات
├── public/             # الملفات العامة والواجهة الأمامية
├── vendor/             # ملفات البائعين
└── README.md           # ملف التوثيق
```

---

## متطلبات التشغيل

- **PHP 7.4+**
- **MySQL 5.7+**
- **Apache/Nginx**
- **مساحة تخزين كافية للملفات المرفوعة**

---

## التثبيت والإعداد

1. **نسخ الملفات:**
   ```bash
   git clone [repository-url]
   cd zouk_market
   ```

2. **إعداد قاعدة البيانات:**
   - إنشاء قاعدة بيانات جديدة
   - استيراد ملف SQL من مجلد `database/`
   - تحديث إعدادات الاتصال في `config/database.php`

3. **إعداد الصلاحيات:**
   ```bash
   chmod 755 public/uploads/
   chmod 755 public/uploads/products/
   chmod 755 public/uploads/stores/
   chmod 755 public/uploads/users/
   ```

4. **تحديث الإعدادات:**
   - تعديل `config/config.php`
   - تحديث `config/settings.php`

---

## الاستخدام

### الوصول للنظام:
- **الصفحة الرئيسية:** `http://your-domain/public/index.php`
- **لوحة تحكم الإدارة:** `http://your-domain/admin/`
- **لوحة تحكم البائع:** `http://your-domain/vendor/`

### حسابات افتراضية:
- **المدير:** <EMAIL>
- **البائع:** <EMAIL>
- **العميل:** <EMAIL>

---

## الأمان

- حماية من هجمات CSRF
- تشفير كلمات المرور
- تنظيف البيانات المدخلة
- التحقق من صحة الملفات المرفوعة
- إدارة الجلسات الآمنة

---

## المساهمة في المشروع

هذا المشروع تم تطويره لأغراض أكاديمية وبحثية. للاستفسارات أو المساهمة:

**الاتصال:**
- **الباحثة:** هبة محمد عاشور عبد الرحمن
- **الهاتف:** +972 59-704-9890
- **البريد الإلكتروني:** <EMAIL>

**المشرف الأكاديمي:**
- **د. طارق عبيد**
- **المؤسسة:** الجامعة الإسلامية - غزة

---

## الترخيص

هذا المشروع مطور لأغراض أكاديمية وبحثية في إطار دراسة أنظمة اتصالات الطوارئ في حالات الحروب والكوارث.

---

## ملاحظات مهمة

- تم تطوير هذا النظام كجزء من بحث أكاديمي متخصص
- يهدف المشروع لدراسة التحديات في قطاع غزة
- النظام قابل للتطوير والتحسين المستمر
- يمكن استخدامه كنموذج لأنظمة مشابهة في مناطق أخرى

---

**© 2024 ذوق ماركت - جميع الحقوق محفوظة**  
**تطوير: هبة محمد عاشور عبد الرحمن**  
**إشراف: د. طارق عبيد**
