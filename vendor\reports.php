<?php
// تضمين ملف الإعدادات
require_once '../config/config.php';

// التحقق من تسجيل الدخول ودور المستخدم
if (!isLoggedIn() || !isVendor()) {
    $_SESSION['error'] = 'يجب تسجيل الدخول كبائع للوصول إلى هذه الصفحة';
    redirect(APP_URL . '/public/login.php');
}

// تضمين النماذج المطلوبة
require_once '../models/Store.php';
require_once '../models/Order.php';
require_once '../models/Product.php';

// إنشاء كائنات النماذج
$storeModel = new Store($conn);
$orderModel = new Order($conn);
$productModel = new Product($conn);

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// الحصول على متجر المستخدم
$store = $storeModel->getByUserId($userId);

// إذا لم يكن للمستخدم متجر، قم بتوجيهه لإنشاء متجر
if (!$store) {
    $_SESSION['info'] = 'يجب إنشاء متجر أولاً للوصول إلى لوحة التحكم';
    redirect(APP_URL . '/vendor/create_store.php');
}

// الحصول على معرف المتجر
$storeId = $store['id'];

// الحصول على الفترة الزمنية (إذا وجدت)
$period = isset($_GET['period']) ? $_GET['period'] : 'month';

// تحديد تاريخ البداية والنهاية بناءً على الفترة الزمنية
$endDate = date('Y-m-d');
$startDate = '';

switch ($period) {
    case 'week':
        $startDate = date('Y-m-d', strtotime('-7 days'));
        break;
    case 'month':
        $startDate = date('Y-m-d', strtotime('-30 days'));
        break;
    case 'quarter':
        $startDate = date('Y-m-d', strtotime('-90 days'));
        break;
    case 'year':
        $startDate = date('Y-m-d', strtotime('-365 days'));
        break;
    case 'custom':
        $startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
        $endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
        break;
    default:
        $startDate = date('Y-m-d', strtotime('-30 days'));
}

// الحصول على إحصائيات المبيعات
$salesStats = [];

// إجمالي المبيعات
$sql = "SELECT SUM(total_amount) as total_sales, COUNT(*) as total_orders 
        FROM orders 
        WHERE store_id = ? 
        AND created_at BETWEEN ? AND ? 
        AND status = 'completed'";
$stmt = $conn->prepare($sql);
$stmt->bind_param("iss", $storeId, $startDate, $endDate);
$stmt->execute();
$result = $stmt->get_result();
$salesStats = $result->fetch_assoc();

// عدد الطلبات حسب الحالة
$ordersByStatus = [];
$statuses = ['new', 'processing', 'ready', 'delivering', 'completed', 'cancelled'];

foreach ($statuses as $status) {
    $sql = "SELECT COUNT(*) as count 
            FROM orders 
            WHERE store_id = ? 
            AND created_at BETWEEN ? AND ? 
            AND status = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("isss", $storeId, $startDate, $endDate, $status);
    $stmt->execute();
    $result = $stmt->get_result();
    $ordersByStatus[$status] = $result->fetch_assoc()['count'];
}

// المنتجات الأكثر مبيعاً
$sql = "SELECT p.id, p.name, p.image, SUM(oi.quantity) as total_quantity, SUM(oi.quantity * oi.price) as total_sales
        FROM order_items oi
        JOIN orders o ON oi.order_id = o.id
        JOIN products p ON oi.product_id = p.id
        WHERE o.store_id = ? 
        AND o.created_at BETWEEN ? AND ? 
        AND o.status = 'completed'
        GROUP BY p.id
        ORDER BY total_quantity DESC
        LIMIT 5";
$stmt = $conn->prepare($sql);
$stmt->bind_param("iss", $storeId, $startDate, $endDate);
$stmt->execute();
$result = $stmt->get_result();
$topProducts = [];

while ($row = $result->fetch_assoc()) {
    $topProducts[] = $row;
}

// المبيعات اليومية للرسم البياني
$sql = "SELECT DATE(created_at) as date, SUM(total_amount) as total
        FROM orders
        WHERE store_id = ? 
        AND created_at BETWEEN ? AND ? 
        AND status = 'completed'
        GROUP BY DATE(created_at)
        ORDER BY date";
$stmt = $conn->prepare($sql);
$stmt->bind_param("iss", $storeId, $startDate, $endDate);
$stmt->execute();
$result = $stmt->get_result();
$dailySales = [];
$salesDates = [];
$salesValues = [];

while ($row = $result->fetch_assoc()) {
    $dailySales[] = $row;
    $salesDates[] = $row['date'];
    $salesValues[] = $row['total'];
}

// تضمين الرأس
include_once '../includes/vendor_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- القائمة الجانبية -->
        <div class="col-md-3 col-lg-2 d-md-block bg-dark dashboard-sidebar">
            <?php include_once 'includes/sidebar.php'; ?>
        </div>
        
        <!-- المحتوى الرئيسي -->
        <div class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تقارير المبيعات</h1>
            </div>
            
            <!-- فلاتر التقارير -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form action="" method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="period" class="form-label">الفترة الزمنية</label>
                            <select class="form-select" id="period" name="period" onchange="toggleCustomDates()">
                                <option value="week" <?php echo ($period == 'week') ? 'selected' : ''; ?>>آخر أسبوع</option>
                                <option value="month" <?php echo ($period == 'month') ? 'selected' : ''; ?>>آخر شهر</option>
                                <option value="quarter" <?php echo ($period == 'quarter') ? 'selected' : ''; ?>>آخر 3 أشهر</option>
                                <option value="year" <?php echo ($period == 'year') ? 'selected' : ''; ?>>آخر سنة</option>
                                <option value="custom" <?php echo ($period == 'custom') ? 'selected' : ''; ?>>فترة مخصصة</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3 custom-date <?php echo ($period != 'custom') ? 'd-none' : ''; ?>">
                            <label for="start_date" class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
                        </div>
                        
                        <div class="col-md-3 custom-date <?php echo ($period != 'custom') ? 'd-none' : ''; ?>">
                            <label for="end_date" class="form-label">تاريخ النهاية</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
                        </div>
                        
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">عرض التقرير</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- ملخص المبيعات -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card bg-primary text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">إجمالي المبيعات</h6>
                                    <h2 class="mb-0"><?php echo number_format($salesStats['total_sales'] ?? 0, 2); ?> شيكل</h2>
                                </div>
                                <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card bg-success text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">إجمالي الطلبات المكتملة</h6>
                                    <h2 class="mb-0"><?php echo $ordersByStatus['completed'] ?? 0; ?></h2>
                                </div>
                                <i class="fas fa-shopping-cart fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card bg-info text-white h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-title">متوسط قيمة الطلب</h6>
                                    <h2 class="mb-0">
                                        <?php
                                        $avgOrderValue = 0;
                                        if (($ordersByStatus['completed'] ?? 0) > 0 && ($salesStats['total_sales'] ?? 0) > 0) {
                                            $avgOrderValue = ($salesStats['total_sales'] ?? 0) / ($ordersByStatus['completed'] ?? 1);
                                        }
                                        echo number_format($avgOrderValue, 2);
                                        ?> شيكل
                                    </h2>
                                </div>
                                <i class="fas fa-chart-line fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الرسم البياني للمبيعات -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">المبيعات اليومية</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- الطلبات حسب الحالة -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">الطلبات حسب الحالة</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="orderStatusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- المنتجات الأكثر مبيعاً -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">المنتجات الأكثر مبيعاً</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($topProducts)): ?>
                                <div class="alert alert-info">
                                    <p class="mb-0">لا توجد بيانات مبيعات للمنتجات خلال الفترة المحددة.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th width="60">الصورة</th>
                                                <th>المنتج</th>
                                                <th>الكمية المباعة</th>
                                                <th>إجمالي المبيعات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($topProducts as $product): ?>
                                                <tr>
                                                    <td>
                                                        <?php
                                                        $imagePath = !empty($product['image']) ? APP_URL . '/public/uploads/products/' . $product['image'] : APP_URL . '/public/img/default_product.png';
                                                        ?>
                                                        <img src="<?php echo $imagePath; ?>" alt="<?php echo $product['name']; ?>" class="img-thumbnail" width="50">
                                                    </td>
                                                    <td><?php echo $product['name']; ?></td>
                                                    <td><?php echo $product['total_quantity']; ?></td>
                                                    <td><?php echo number_format($product['total_sales'], 2); ?> شيكل</td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دالة لتبديل حقول التاريخ المخصصة
function toggleCustomDates() {
    const period = document.getElementById('period').value;
    const customDateFields = document.querySelectorAll('.custom-date');
    
    if (period === 'custom') {
        customDateFields.forEach(field => field.classList.remove('d-none'));
    } else {
        customDateFields.forEach(field => field.classList.add('d-none'));
    }
}

// إنشاء الرسوم البيانية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // رسم بياني للمبيعات اليومية
    const salesChartCtx = document.getElementById('salesChart').getContext('2d');
    const salesChart = new Chart(salesChartCtx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($salesDates); ?>,
            datasets: [{
                label: 'المبيعات اليومية (شيكل)',
                data: <?php echo json_encode($salesValues); ?>,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // رسم بياني للطلبات حسب الحالة
    const orderStatusChartCtx = document.getElementById('orderStatusChart').getContext('2d');
    const orderStatusChart = new Chart(orderStatusChartCtx, {
        type: 'doughnut',
        data: {
            labels: ['جديد', 'قيد التجهيز', 'جاهز للتسليم', 'قيد التوصيل', 'مكتمل', 'ملغي'],
            datasets: [{
                data: [
                    <?php echo $ordersByStatus['new'] ?? 0; ?>,
                    <?php echo $ordersByStatus['processing'] ?? 0; ?>,
                    <?php echo $ordersByStatus['ready'] ?? 0; ?>,
                    <?php echo $ordersByStatus['delivering'] ?? 0; ?>,
                    <?php echo $ordersByStatus['completed'] ?? 0; ?>,
                    <?php echo $ordersByStatus['cancelled'] ?? 0; ?>
                ],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 193, 7, 0.7)',
                    'rgba(23, 162, 184, 0.7)',
                    'rgba(13, 202, 240, 0.7)',
                    'rgba(40, 167, 69, 0.7)',
                    'rgba(220, 53, 69, 0.7)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>

<?php
// تضمين التذييل
include_once '../includes/vendor_footer.php';
?>
